import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import '../config/monday_dashboard_config.dart';

/// أنواع المشاركة المدعومة
enum ShareType {
  link,           // رابط مشاركة
  embed,          // كود التضمين
  pdf,            // تصدير PDF
  image,          // تصدير صورة
  presentation,   // عرض تقديمي
  email,          // إرسال بالبريد الإلكتروني
}

/// مستويات الصلاحيات
enum PermissionLevel {
  view,           // عرض فقط
  comment,        // عرض وتعليق
  edit,           // عرض وتحرير
  admin,          // صلاحيات كاملة
}

/// إعدادات المشاركة
class ShareSettings {
  final ShareType type;
  final PermissionLevel permission;
  final bool requirePassword;
  final String? password;
  final DateTime? expiryDate;
  final List<String> allowedEmails;
  final bool allowPublicAccess;
  final Map<String, dynamic> customSettings;

  const ShareSettings({
    required this.type,
    this.permission = PermissionLevel.view,
    this.requirePassword = false,
    this.password,
    this.expiryDate,
    this.allowedEmails = const [],
    this.allowPublicAccess = false,
    this.customSettings = const {},
  });
}

/// خدمة المشاركة والعرض التقديمي بمعايير Monday.com
class MondaySharingService {

  /// مشاركة لوحة التحكم
  static Future<String?> shareDashboard({
    required String dashboardId,
    required ShareSettings settings,
    BuildContext? context,
  }) async {
    try {
      switch (settings.type) {
        case ShareType.link:
          return await _generateShareLink(dashboardId, settings);
        case ShareType.embed:
          return await _generateEmbedCode(dashboardId, settings);
        case ShareType.pdf:
          return await _exportToPDF(dashboardId, settings);
        case ShareType.image:
          return await _exportToImage(dashboardId, settings);
        case ShareType.presentation:
          return await _createPresentation(dashboardId, settings);
        case ShareType.email:
          return await _shareViaEmail(dashboardId, settings);
      }
    } catch (e) {
      debugPrint('خطأ في مشاركة لوحة التحكم: $e');
      return null;
    }
  }

  /// توليد رابط المشاركة
  static Future<String> _generateShareLink(
    String dashboardId,
    ShareSettings settings,
  ) async {
    final baseUrl = 'https://dashboard.app.com/shared';
    final shareToken = _generateShareToken(dashboardId, settings);
    
    final queryParams = <String, String>{
      'token': shareToken,
      'permission': settings.permission.name,
      'dashboard': dashboardId,
    };

    if (settings.requirePassword) {
      queryParams['protected'] = 'true';
    }

    if (settings.expiryDate != null) {
      queryParams['expires'] = settings.expiryDate!.millisecondsSinceEpoch.toString();
    }

    final uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);
    return uri.toString();
  }

  /// توليد كود التضمين
  static Future<String> _generateEmbedCode(
    String dashboardId,
    ShareSettings settings,
  ) async {
    final shareLink = await _generateShareLink(dashboardId, settings);
    
    return '''
<iframe 
  src="$shareLink&embed=true" 
  width="100%" 
  height="600" 
  frameborder="0" 
  allowfullscreen>
</iframe>
''';
  }

  /// تصدير إلى PDF
  static Future<String> _exportToPDF(
    String dashboardId,
    ShareSettings settings,
  ) async {
    // تنفيذ تصدير PDF
    // يمكن استخدام مكتبة pdf أو طلب API
    return 'dashboard_$dashboardId.pdf';
  }

  /// تصدير إلى صورة
  static Future<String> _exportToImage(
    String dashboardId,
    ShareSettings settings,
  ) async {
    // تنفيذ تصدير الصورة
    return 'dashboard_$dashboardId.png';
  }

  /// إنشاء عرض تقديمي
  static Future<String> _createPresentation(
    String dashboardId,
    ShareSettings settings,
  ) async {
    // تنفيذ إنشاء العرض التقديمي
    return 'presentation_$dashboardId.pptx';
  }

  /// مشاركة عبر البريد الإلكتروني
  static Future<String> _shareViaEmail(
    String dashboardId,
    ShareSettings settings,
  ) async {
    final shareLink = await _generateShareLink(dashboardId, settings);
    
    final subject = 'مشاركة لوحة التحكم';
    final body = '''
مرحباً،

تم مشاركة لوحة تحكم معك. يمكنك الوصول إليها من خلال الرابط التالي:

$shareLink

${settings.requirePassword ? 'ملاحظة: هذه اللوحة محمية بكلمة مرور.' : ''}
${settings.expiryDate != null ? 'ينتهي صلاحية الرابط في: ${settings.expiryDate}' : ''}

مع تحياتي
''';

    await Share.share(
      '$subject\n\n$body',
      subject: subject,
    );

    return shareLink;
  }

  /// توليد رمز المشاركة
  static String _generateShareToken(String dashboardId, ShareSettings settings) {
    // توليد رمز فريد للمشاركة
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 1000000).toString();
    return '${dashboardId}_${settings.permission.name}_$random';
  }

  /// عرض حوار المشاركة
  static Future<void> showShareDialog({
    required BuildContext context,
    required String dashboardId,
    required String dashboardTitle,
  }) async {
    return showDialog(
      context: context,
      builder: (context) => _ShareDialog(
        dashboardId: dashboardId,
        dashboardTitle: dashboardTitle,
      ),
    );
  }
}

/// حوار المشاركة
class _ShareDialog extends StatefulWidget {
  final String dashboardId;
  final String dashboardTitle;

  const _ShareDialog({
    required this.dashboardId,
    required this.dashboardTitle,
  });

  @override
  State<_ShareDialog> createState() => _ShareDialogState();
}

class _ShareDialogState extends State<_ShareDialog> {
  ShareType _selectedType = ShareType.link;
  PermissionLevel _selectedPermission = PermissionLevel.view;
  bool _requirePassword = false;
  bool _setExpiry = false;
  DateTime? _expiryDate;
  String? _generatedLink;
  bool _isGenerating = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
      ),
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildShareTypeSelector(),
            const SizedBox(height: 16),
            _buildPermissionSelector(),
            const SizedBox(height: 16),
            _buildSecurityOptions(),
            const SizedBox(height: 24),
            _buildGeneratedLink(),
            const SizedBox(height: 24),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الحوار
  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: MondayDashboardConfig.mondayBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.share,
            color: MondayDashboardConfig.mondayBlue,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'مشاركة لوحة التحكم',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: MondayDashboardConfig.textPrimary,
                ),
              ),
              Text(
                widget.dashboardTitle,
                style: TextStyle(
                  fontSize: 14,
                  color: MondayDashboardConfig.textMuted,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.close,
            color: MondayDashboardConfig.textMuted,
          ),
        ),
      ],
    );
  }

  /// بناء محدد نوع المشاركة
  Widget _buildShareTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع المشاركة',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: MondayDashboardConfig.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: ShareType.values.map((type) {
            final isSelected = _selectedType == type;
            return FilterChip(
              label: Text(_getShareTypeLabel(type)),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedType = type;
                    _generatedLink = null;
                  });
                }
              },
              backgroundColor: MondayDashboardConfig.hoverColor,
              selectedColor: MondayDashboardConfig.mondayBlue.withOpacity(0.1),
              checkmarkColor: MondayDashboardConfig.mondayBlue,
              labelStyle: TextStyle(
                color: isSelected 
                    ? MondayDashboardConfig.mondayBlue
                    : MondayDashboardConfig.textSecondary,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// بناء محدد الصلاحيات
  Widget _buildPermissionSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مستوى الصلاحية',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: MondayDashboardConfig.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<PermissionLevel>(
          value: _selectedPermission,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: PermissionLevel.values.map((permission) {
            return DropdownMenuItem(
              value: permission,
              child: Row(
                children: [
                  Icon(
                    _getPermissionIcon(permission),
                    size: 16,
                    color: MondayDashboardConfig.textSecondary,
                  ),
                  const SizedBox(width: 8),
                  Text(_getPermissionLabel(permission)),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedPermission = value;
                _generatedLink = null;
              });
            }
          },
        ),
      ],
    );
  }

  /// بناء خيارات الأمان
  Widget _buildSecurityOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'خيارات الأمان',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: MondayDashboardConfig.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        CheckboxListTile(
          title: const Text('حماية بكلمة مرور'),
          value: _requirePassword,
          onChanged: (value) {
            setState(() {
              _requirePassword = value ?? false;
              _generatedLink = null;
            });
          },
          dense: true,
          contentPadding: EdgeInsets.zero,
        ),
        CheckboxListTile(
          title: const Text('تحديد تاريخ انتهاء'),
          value: _setExpiry,
          onChanged: (value) {
            setState(() {
              _setExpiry = value ?? false;
              if (!_setExpiry) _expiryDate = null;
              _generatedLink = null;
            });
          },
          dense: true,
          contentPadding: EdgeInsets.zero,
        ),
        if (_setExpiry) ...[
          const SizedBox(height: 8),
          InkWell(
            onTap: _selectExpiryDate,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: MondayDashboardConfig.borderColor),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: MondayDashboardConfig.textSecondary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _expiryDate != null 
                        ? '${_expiryDate!.day}/${_expiryDate!.month}/${_expiryDate!.year}'
                        : 'اختر تاريخ الانتهاء',
                    style: TextStyle(
                      color: _expiryDate != null 
                          ? MondayDashboardConfig.textPrimary
                          : MondayDashboardConfig.textMuted,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// بناء الرابط المولد
  Widget _buildGeneratedLink() {
    if (_generatedLink == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الرابط المولد',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: MondayDashboardConfig.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: MondayDashboardConfig.hoverColor,
            border: Border.all(color: MondayDashboardConfig.borderColor),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  _generatedLink!,
                  style: TextStyle(
                    fontSize: 12,
                    color: MondayDashboardConfig.textSecondary,
                    fontFamily: 'monospace',
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              IconButton(
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: _generatedLink!));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم نسخ الرابط')),
                  );
                },
                icon: Icon(
                  Icons.copy,
                  size: 16,
                  color: MondayDashboardConfig.mondayBlue,
                ),
                tooltip: 'نسخ الرابط',
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء الإجراءات
  Widget _buildActions() {
    return Row(
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        const Spacer(),
        ElevatedButton(
          onPressed: _isGenerating ? null : _generateShare,
          style: ElevatedButton.styleFrom(
            backgroundColor: MondayDashboardConfig.mondayBlue,
            foregroundColor: Colors.white,
          ),
          child: _isGenerating
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('إنشاء رابط المشاركة'),
        ),
      ],
    );
  }

  /// توليد المشاركة
  Future<void> _generateShare() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      final settings = ShareSettings(
        type: _selectedType,
        permission: _selectedPermission,
        requirePassword: _requirePassword,
        expiryDate: _expiryDate,
      );

      final result = await MondaySharingService.shareDashboard(
        dashboardId: widget.dashboardId,
        settings: settings,
        context: context,
      );

      if (result != null) {
        setState(() {
          _generatedLink = result;
        });
      }
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  /// اختيار تاريخ الانتهاء
  Future<void> _selectExpiryDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _expiryDate = date;
        _generatedLink = null;
      });
    }
  }

  /// الحصول على تسمية نوع المشاركة
  String _getShareTypeLabel(ShareType type) {
    switch (type) {
      case ShareType.link:
        return 'رابط';
      case ShareType.embed:
        return 'تضمين';
      case ShareType.pdf:
        return 'PDF';
      case ShareType.image:
        return 'صورة';
      case ShareType.presentation:
        return 'عرض تقديمي';
      case ShareType.email:
        return 'بريد إلكتروني';
    }
  }

  /// الحصول على تسمية مستوى الصلاحية
  String _getPermissionLabel(PermissionLevel permission) {
    switch (permission) {
      case PermissionLevel.view:
        return 'عرض فقط';
      case PermissionLevel.comment:
        return 'عرض وتعليق';
      case PermissionLevel.edit:
        return 'عرض وتحرير';
      case PermissionLevel.admin:
        return 'صلاحيات كاملة';
    }
  }

  /// الحصول على أيقونة مستوى الصلاحية
  IconData _getPermissionIcon(PermissionLevel permission) {
    switch (permission) {
      case PermissionLevel.view:
        return Icons.visibility;
      case PermissionLevel.comment:
        return Icons.comment;
      case PermissionLevel.edit:
        return Icons.edit;
      case PermissionLevel.admin:
        return Icons.admin_panel_settings;
    }
  }
}
