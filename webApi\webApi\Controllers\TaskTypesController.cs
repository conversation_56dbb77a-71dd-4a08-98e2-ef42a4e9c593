using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة أنواع المهام
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class TaskTypesController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public TaskTypesController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع أنواع المهام
        /// </summary>
        /// <returns>قائمة بجميع أنواع المهام</returns>
        /// <response code="200">إرجاع قائمة أنواع المهام</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskType>>> GetTaskTypes()
        {
            return await _context.TaskTypes
                .OrderBy(tt => tt.Name)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على نوع مهمة محدد
        /// </summary>
        /// <param name="id">معرف نوع المهمة</param>
        /// <returns>نوع المهمة المطلوب</returns>
        /// <response code="200">إرجاع نوع المهمة</response>
        /// <response code="404">نوع المهمة غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TaskType>> GetTaskType(int id)
        {
            var taskType = await _context.TaskTypes.FindAsync(id);

            if (taskType == null)
            {
                return NotFound();
            }

            return taskType;
        }

        /// <summary>
        /// الحصول على المهام حسب نوع المهمة
        /// </summary>
        /// <param name="id">معرف نوع المهمة</param>
        /// <returns>قائمة المهام من هذا النوع</returns>
        /// <response code="200">إرجاع قائمة المهام</response>
        [HttpGet("{id}/tasks")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetTasksByType(int id)
        {
            return await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.Assignee)
                .Include(t => t.Department)
                .Include(t => t.TaskType)
                .Where(t => t.TaskTypeId == id && !t.IsDeleted)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// عدد المهام حسب نوع المهمة
        /// </summary>
        /// <param name="id">معرف نوع المهمة</param>
        /// <returns>عدد المهام</returns>
        /// <response code="200">إرجاع العدد</response>
        [HttpGet("{id}/tasks/count")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<int>> GetTasksCountByType(int id)
        {
            var count = await _context.Tasks
                .CountAsync(t => t.TaskTypeId == id && !t.IsDeleted);

            return count;
        }

        /// <summary>
        /// الحصول على أنواع المهام الافتراضية
        /// </summary>
        /// <returns>قائمة أنواع المهام الافتراضية</returns>
        /// <response code="200">إرجاع قائمة أنواع المهام الافتراضية</response>
        [HttpGet("default")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskType>>> GetDefaultTaskTypes()
        {
            return await _context.TaskTypes
                .Where(tt => tt.IsDefault == true)
                .OrderBy(tt => tt.Name)
                .ToListAsync();
        }

        /// <summary>
        /// البحث في أنواع المهام
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة أنواع المهام المطابقة</returns>
        /// <response code="200">إرجاع قائمة أنواع المهام المطابقة</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskType>>> SearchTaskTypes([FromQuery] string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetTaskTypes();
            }

            var searchTermLower = searchTerm.ToLower();
            return await _context.TaskTypes
                .Where(tt => tt.Name.ToLower().Contains(searchTermLower) ||
                           (tt.Description != null && tt.Description.ToLower().Contains(searchTermLower)))
                .OrderBy(tt => tt.Name)
                .ToListAsync();
        }

        /// <summary>
        /// تعيين نوع مهمة كافتراضي
        /// </summary>
        /// <param name="id">معرف نوع المهمة</param>
        /// <returns>نتيجة العملية</returns>
        /// <response code="204">تم تعيين نوع المهمة كافتراضي بنجاح</response>
        /// <response code="404">نوع المهمة غير موجود</response>
        [HttpPatch("{id}/set-default")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SetAsDefault(int id)
        {
            var taskType = await _context.TaskTypes.FindAsync(id);
            if (taskType == null)
            {
                return NotFound();
            }

            // إزالة الافتراضي من جميع الأنواع الأخرى
            var allTaskTypes = await _context.TaskTypes.ToListAsync();
            foreach (var type in allTaskTypes)
            {
                type.IsDefault = type.Id == id;
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        /// <summary>
        /// إنشاء نوع مهمة جديد
        /// </summary>
        /// <param name="taskType">بيانات نوع المهمة</param>
        /// <returns>نوع المهمة المُنشأ</returns>
        /// <response code="201">تم إنشاء نوع المهمة بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TaskType>> PostTaskType(TaskType taskType)
        {
            // التحقق من عدم وجود نوع مهمة بنفس الاسم
            var existingTaskType = await _context.TaskTypes
                .FirstOrDefaultAsync(tt => tt.Name.ToLower() == taskType.Name.ToLower());

            if (existingTaskType != null)
            {
                return BadRequest("يوجد نوع مهمة بهذا الاسم مسبقاً");
            }

            taskType.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            _context.TaskTypes.Add(taskType);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetTaskType", new { id = taskType.Id }, taskType);
        }

        /// <summary>
        /// تحديث نوع مهمة
        /// </summary>
        /// <param name="id">معرف نوع المهمة</param>
        /// <param name="taskType">بيانات نوع المهمة المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث نوع المهمة بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">نوع المهمة غير موجود</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutTaskType(int id, TaskType taskType)
        {
            if (id != taskType.Id)
            {
                return BadRequest();
            }

            // التحقق من عدم وجود نوع مهمة آخر بنفس الاسم
            var existingTaskType = await _context.TaskTypes
                .FirstOrDefaultAsync(tt => tt.Name.ToLower() == taskType.Name.ToLower() && tt.Id != id);

            if (existingTaskType != null)
            {
                return BadRequest("يوجد نوع مهمة آخر بهذا الاسم مسبقاً");
            }

            _context.Entry(taskType).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TaskTypeExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// تبديل حالة نوع المهمة كافتراضي
        /// </summary>
        /// <param name="id">معرف نوع المهمة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تبديل حالة نوع المهمة كافتراضي</response>
        /// <response code="404">نوع المهمة غير موجود</response>
        [HttpPatch("{id}/toggle-default")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ToggleAsDefault(int id)
        {
            var taskType = await _context.TaskTypes.FindAsync(id);
            if (taskType == null)
            {
                return NotFound();
            }

            // إذا كان سيصبح افتراضي، قم بإزالة الافتراضي من الآخرين
            if (!taskType.IsDefault)
            {
                var currentDefault = await _context.TaskTypes.FirstOrDefaultAsync(tt => tt.IsDefault);
                if (currentDefault != null)
                {
                    currentDefault.IsDefault = false;
                }
                taskType.IsDefault = true;
            }
            else
            {
                taskType.IsDefault = false;
            }

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// إلغاء تعيين نوع مهمة كافتراضي
        /// </summary>
        /// <param name="id">معرف نوع المهمة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم إلغاء تعيين نوع المهمة كافتراضي</response>
        /// <response code="404">نوع المهمة غير موجود</response>
        [HttpPatch("{id}/unset-default")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UnsetAsDefault(int id)
        {
            var taskType = await _context.TaskTypes.FindAsync(id);
            if (taskType == null)
            {
                return NotFound();
            }

            taskType.IsDefault = false;
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// حذف نوع مهمة
        /// </summary>
        /// <param name="id">معرف نوع المهمة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف نوع المهمة بنجاح</response>
        /// <response code="400">لا يمكن حذف نوع المهمة لوجود مهام مرتبطة به</response>
        /// <response code="404">نوع المهمة غير موجود</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTaskType(int id)
        {
            var taskType = await _context.TaskTypes.FindAsync(id);
            if (taskType == null)
            {
                return NotFound();
            }

            // التحقق من عدم وجود مهام مرتبطة بهذا النوع
            var hasRelatedTasks = await _context.Tasks
                .AnyAsync(t => t.TaskTypeId == id && !t.IsDeleted);

            if (hasRelatedTasks)
            {
                return BadRequest("لا يمكن حذف نوع المهمة لوجود مهام مرتبطة به");
            }

            _context.TaskTypes.Remove(taskType);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool TaskTypeExists(int id)
        {
            return _context.TaskTypes.Any(e => e.Id == id);
        }
    }
}
