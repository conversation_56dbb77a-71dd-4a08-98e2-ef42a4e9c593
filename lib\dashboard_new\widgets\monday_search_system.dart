import 'package:flutter/material.dart';
import '../config/monday_dashboard_config.dart';

/// نظام البحث المتقدم بمعايير Monday.com
class MondaySearchSystem extends StatefulWidget {
  final Function(String) onSearch;
  final Function(Map<String, dynamic>) onAdvancedSearch;
  final List<String> suggestions;
  final List<String> recentSearches;
  final bool showAdvancedOptions;
  final String? placeholder;

  const MondaySearchSystem({
    super.key,
    required this.onSearch,
    required this.onAdvancedSearch,
    this.suggestions = const [],
    this.recentSearches = const [],
    this.showAdvancedOptions = true,
    this.placeholder,
  });

  @override
  State<MondaySearchSystem> createState() => _MondaySearchSystemState();
}

class _MondaySearchSystemState extends State<MondaySearchSystem>
    with TickerProviderStateMixin {
  late final TextEditingController _searchController;
  late final FocusNode _searchFocusNode;
  late final AnimationController _animationController;
  late final Animation<double> _fadeAnimation;
  
  bool _showSuggestions = false;
  bool _showAdvanced = false;
  List<String> _filteredSuggestions = [];
  final Map<String, dynamic> _advancedFilters = {};

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _searchFocusNode = FocusNode();
    
    _animationController = AnimationController(
      duration: MondayDashboardConfig.animationDuration,
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: MondayDashboardConfig.animationCurve,
    ));

    _searchController.addListener(_onSearchChanged);
    _searchFocusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchBar(),
        if (_showSuggestions || _showAdvanced) ...[
          const SizedBox(height: 8),
          FadeTransition(
            opacity: _fadeAnimation,
            child: _buildSearchDropdown(),
          ),
        ],
      ],
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _searchFocusNode.hasFocus 
              ? MondayDashboardConfig.mondayBlue
              : MondayDashboardConfig.borderColor,
          width: _searchFocusNode.hasFocus ? 2 : 1,
        ),
        boxShadow: _searchFocusNode.hasFocus ? [
          BoxShadow(
            color: MondayDashboardConfig.mondayBlue.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Row(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Icon(
              Icons.search,
              color: _searchFocusNode.hasFocus 
                  ? MondayDashboardConfig.mondayBlue
                  : MondayDashboardConfig.textMuted,
              size: 20,
            ),
          ),
          Expanded(
            child: TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: widget.placeholder ?? 'البحث في لوحة التحكم...',
                hintStyle: TextStyle(
                  color: MondayDashboardConfig.textMuted,
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              style: TextStyle(
                color: MondayDashboardConfig.textPrimary,
                fontSize: 14,
              ),
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  widget.onSearch(value);
                  _addToRecentSearches(value);
                }
              },
            ),
          ),
          if (_searchController.text.isNotEmpty) ...[
            IconButton(
              onPressed: _clearSearch,
              icon: Icon(
                Icons.clear,
                color: MondayDashboardConfig.textMuted,
                size: 18,
              ),
              tooltip: 'مسح البحث',
            ),
          ],
          if (widget.showAdvancedOptions) ...[
            Container(
              height: 24,
              width: 1,
              color: MondayDashboardConfig.borderColor,
              margin: const EdgeInsets.symmetric(horizontal: 8),
            ),
            IconButton(
              onPressed: _toggleAdvancedSearch,
              icon: Icon(
                _showAdvanced ? Icons.tune : Icons.tune,
                color: _showAdvanced 
                    ? MondayDashboardConfig.mondayBlue
                    : MondayDashboardConfig.textMuted,
                size: 18,
              ),
              tooltip: _showAdvanced ? 'إخفاء البحث المتقدم' : 'البحث المتقدم',
            ),
          ],
        ],
      ),
    );
  }

  /// بناء قائمة البحث المنسدلة
  Widget _buildSearchDropdown() {
    return Container(
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: MondayDashboardConfig.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          if (_showAdvanced) _buildAdvancedSearch(),
          if (_showSuggestions && !_showAdvanced) _buildSuggestions(),
        ],
      ),
    );
  }

  /// بناء البحث المتقدم
  Widget _buildAdvancedSearch() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.tune,
                color: MondayDashboardConfig.mondayBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'البحث المتقدم',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: MondayDashboardConfig.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildAdvancedFilters(),
          const SizedBox(height: 16),
          _buildAdvancedActions(),
        ],
      ),
    );
  }

  /// بناء فلاتر البحث المتقدم
  Widget _buildAdvancedFilters() {
    return Column(
      children: [
        // فلتر نوع البيانات
        _buildFilterRow(
          'نوع البيانات',
          DropdownButtonFormField<String>(
            decoration: _getInputDecoration(),
            hint: const Text('اختر نوع البيانات'),
            items: const [
              DropdownMenuItem(value: 'tasks', child: Text('المهام')),
              DropdownMenuItem(value: 'users', child: Text('المستخدمون')),
              DropdownMenuItem(value: 'departments', child: Text('الأقسام')),
              DropdownMenuItem(value: 'projects', child: Text('المشاريع')),
            ],
            onChanged: (value) {
              setState(() {
                _advancedFilters['dataType'] = value;
              });
            },
          ),
        ),
        const SizedBox(height: 12),
        
        // فلتر النطاق الزمني
        _buildFilterRow(
          'النطاق الزمني',
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  decoration: _getInputDecoration().copyWith(
                    hintText: 'من تاريخ',
                    suffixIcon: const Icon(Icons.calendar_today, size: 16),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate('startDate'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: TextFormField(
                  decoration: _getInputDecoration().copyWith(
                    hintText: 'إلى تاريخ',
                    suffixIcon: const Icon(Icons.calendar_today, size: 16),
                  ),
                  readOnly: true,
                  onTap: () => _selectDate('endDate'),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        
        // فلتر الحالة
        _buildFilterRow(
          'الحالة',
          Wrap(
            spacing: 8,
            children: ['مكتملة', 'قيد التنفيذ', 'معلقة', 'متأخرة'].map((status) {
              final isSelected = (_advancedFilters['status'] as List<String>?)?.contains(status) ?? false;
              return FilterChip(
                label: Text(status),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    final statusList = (_advancedFilters['status'] as List<String>?) ?? <String>[];
                    if (selected) {
                      statusList.add(status);
                    } else {
                      statusList.remove(status);
                    }
                    _advancedFilters['status'] = statusList;
                  });
                },
                backgroundColor: MondayDashboardConfig.hoverColor,
                selectedColor: MondayDashboardConfig.mondayBlue.withOpacity(0.1),
                checkmarkColor: MondayDashboardConfig.mondayBlue,
              );
            }).toList(),
          ),
        ),
        const SizedBox(height: 12),
        
        // فلتر الأولوية
        _buildFilterRow(
          'الأولوية',
          DropdownButtonFormField<String>(
            decoration: _getInputDecoration(),
            hint: const Text('اختر الأولوية'),
            items: const [
              DropdownMenuItem(value: 'high', child: Text('عالية')),
              DropdownMenuItem(value: 'medium', child: Text('متوسطة')),
              DropdownMenuItem(value: 'low', child: Text('منخفضة')),
              DropdownMenuItem(value: 'critical', child: Text('حرجة')),
            ],
            onChanged: (value) {
              setState(() {
                _advancedFilters['priority'] = value;
              });
            },
          ),
        ),
      ],
    );
  }

  /// بناء صف فلتر
  Widget _buildFilterRow(String label, Widget child) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: MondayDashboardConfig.textSecondary,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(child: child),
      ],
    );
  }

  /// بناء إجراءات البحث المتقدم
  Widget _buildAdvancedActions() {
    return Row(
      children: [
        TextButton(
          onPressed: _clearAdvancedFilters,
          child: Text(
            'مسح الفلاتر',
            style: TextStyle(
              color: MondayDashboardConfig.textMuted,
            ),
          ),
        ),
        const Spacer(),
        ElevatedButton(
          onPressed: _performAdvancedSearch,
          style: ElevatedButton.styleFrom(
            backgroundColor: MondayDashboardConfig.mondayBlue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
          ),
          child: const Text('بحث'),
        ),
      ],
    );
  }

  /// بناء الاقتراحات
  Widget _buildSuggestions() {
    return Column(
      children: [
        if (_filteredSuggestions.isNotEmpty) ...[
          _buildSuggestionSection('اقتراحات', _filteredSuggestions),
        ],
        if (widget.recentSearches.isNotEmpty && _searchController.text.isEmpty) ...[
          _buildSuggestionSection('عمليات البحث الأخيرة', widget.recentSearches),
        ],
        if (_filteredSuggestions.isEmpty && widget.recentSearches.isEmpty) ...[
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'لا توجد اقتراحات',
              style: TextStyle(
                color: MondayDashboardConfig.textMuted,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// بناء قسم الاقتراحات
  Widget _buildSuggestionSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: MondayDashboardConfig.textMuted,
            ),
          ),
        ),
        ...items.take(5).map((item) => _buildSuggestionItem(item)),
      ],
    );
  }

  /// بناء عنصر اقتراح
  Widget _buildSuggestionItem(String suggestion) {
    return InkWell(
      onTap: () => _selectSuggestion(suggestion),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(
              Icons.search,
              size: 16,
              color: MondayDashboardConfig.textMuted,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                suggestion,
                style: TextStyle(
                  fontSize: 14,
                  color: MondayDashboardConfig.textPrimary,
                ),
              ),
            ),
            Icon(
              Icons.north_west,
              size: 14,
              color: MondayDashboardConfig.textMuted,
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على تزيين المدخل
  InputDecoration _getInputDecoration() {
    return InputDecoration(
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(6),
        borderSide: BorderSide(color: MondayDashboardConfig.borderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(6),
        borderSide: BorderSide(color: MondayDashboardConfig.borderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(6),
        borderSide: BorderSide(color: MondayDashboardConfig.mondayBlue),
      ),
    );
  }

  /// معالجة تغيير البحث
  void _onSearchChanged() {
    final query = _searchController.text;
    
    setState(() {
      _filteredSuggestions = widget.suggestions
          .where((suggestion) => suggestion.toLowerCase().contains(query.toLowerCase()))
          .toList();
    });

    if (query.isNotEmpty) {
      // تنفيذ البحث الفوري
      widget.onSearch(query);
    }
  }

  /// معالجة تغيير التركيز
  void _onFocusChanged() {
    setState(() {
      _showSuggestions = _searchFocusNode.hasFocus && !_showAdvanced;
    });

    if (_searchFocusNode.hasFocus) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  /// مسح البحث
  void _clearSearch() {
    _searchController.clear();
    widget.onSearch('');
  }

  /// تبديل البحث المتقدم
  void _toggleAdvancedSearch() {
    setState(() {
      _showAdvanced = !_showAdvanced;
      _showSuggestions = !_showAdvanced && _searchFocusNode.hasFocus;
    });

    if (_showAdvanced) {
      _animationController.forward();
    }
  }

  /// اختيار اقتراح
  void _selectSuggestion(String suggestion) {
    _searchController.text = suggestion;
    _searchFocusNode.unfocus();
    widget.onSearch(suggestion);
    _addToRecentSearches(suggestion);
  }

  /// إضافة إلى عمليات البحث الأخيرة
  void _addToRecentSearches(String query) {
    // يمكن حفظ عمليات البحث الأخيرة في SharedPreferences
  }

  /// اختيار تاريخ
  Future<void> _selectDate(String key) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: MondayDashboardConfig.mondayBlue,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() {
        _advancedFilters[key] = date;
      });
    }
  }

  /// مسح فلاتر البحث المتقدم
  void _clearAdvancedFilters() {
    setState(() {
      _advancedFilters.clear();
    });
  }

  /// تنفيذ البحث المتقدم
  void _performAdvancedSearch() {
    final searchQuery = _searchController.text;
    final filters = Map<String, dynamic>.from(_advancedFilters);
    
    if (searchQuery.isNotEmpty) {
      filters['query'] = searchQuery;
    }

    widget.onAdvancedSearch(filters);
    _searchFocusNode.unfocus();
    
    if (searchQuery.isNotEmpty) {
      _addToRecentSearches(searchQuery);
    }
  }
}
