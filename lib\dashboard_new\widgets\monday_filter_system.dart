import 'package:flutter/material.dart';
import '../models/dashboard_models.dart';
import '../config/monday_dashboard_config.dart';

/// نظام الفلترة المتقدم بمعايير Monday.com
class MondayFilterSystem extends StatefulWidget {
  final List<DataFilter> availableFilters;
  final Map<String, DataFilter> activeFilters;
  final Function(Map<String, DataFilter>) onFiltersChanged;
  final bool isExpanded;
  final Function(bool)? onExpandedChanged;

  const MondayFilterSystem({
    super.key,
    required this.availableFilters,
    required this.activeFilters,
    required this.onFiltersChanged,
    this.isExpanded = false,
    this.onExpandedChanged,
  });

  @override
  State<MondayFilterSystem> createState() => _MondayFilterSystemState();
}

class _MondayFilterSystemState extends State<MondayFilterSystem>
    with TickerProviderStateMixin {
  late AnimationController _expandController;
  late Animation<double> _expandAnimation;
  late Map<String, DataFilter> _currentFilters;

  @override
  void initState() {
    super.initState();
    _currentFilters = Map.from(widget.activeFilters);
    
    _expandController = AnimationController(
      duration: MondayDashboardConfig.animationDuration,
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _expandController,
      curve: MondayDashboardConfig.animationCurve,
    );

    if (widget.isExpanded) {
      _expandController.forward();
    }
  }

  @override
  void didUpdateWidget(MondayFilterSystem oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _expandController.forward();
      } else {
        _expandController.reverse();
      }
    }
    
    if (widget.activeFilters != oldWidget.activeFilters) {
      setState(() {
        _currentFilters = Map.from(widget.activeFilters);
      });
    }
  }

  @override
  void dispose() {
    _expandController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        border: Border.all(color: MondayDashboardConfig.borderColor),
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildFilterHeader(),
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: _buildFilterContent(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الفلاتر
  Widget _buildFilterHeader() {
    final activeCount = _currentFilters.values.where((f) => f.isActive).length;
    
    return InkWell(
      onTap: () {
        final newExpanded = !widget.isExpanded;
        widget.onExpandedChanged?.call(newExpanded);
      },
      borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
      child: Container(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(
              Icons.filter_list,
              color: activeCount > 0 
                  ? MondayDashboardConfig.mondayBlue
                  : MondayDashboardConfig.textSecondary,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'الفلاتر',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: MondayDashboardConfig.textPrimary,
                ),
              ),
            ),
            if (activeCount > 0) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: MondayDashboardConfig.mondayBlue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  activeCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 8),
            ],
            AnimatedRotation(
              turns: widget.isExpanded ? 0.5 : 0.0,
              duration: MondayDashboardConfig.animationDuration,
              child: Icon(
                Icons.keyboard_arrow_down,
                color: MondayDashboardConfig.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء محتوى الفلاتر
  Widget _buildFilterContent() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        children: [
          const Divider(height: 1),
          const SizedBox(height: 16),
          _buildQuickFilters(),
          const SizedBox(height: 16),
          _buildAdvancedFilters(),
          const SizedBox(height: 16),
          _buildFilterActions(),
        ],
      ),
    );
  }

  /// بناء الفلاتر السريعة
  Widget _buildQuickFilters() {
    final quickFilters = widget.availableFilters
        .where((f) => f.type == FilterType.toggle || f.type == FilterType.dropdown)
        .take(4)
        .toList();

    if (quickFilters.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'فلاتر سريعة',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: MondayDashboardConfig.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: quickFilters.map((filter) => _buildQuickFilterChip(filter)).toList(),
        ),
      ],
    );
  }

  /// بناء رقاقة فلتر سريع
  Widget _buildQuickFilterChip(DataFilter filter) {
    final isActive = _currentFilters[filter.id]?.isActive ?? false;
    
    return FilterChip(
      label: Text(filter.name),
      selected: isActive,
      onSelected: (selected) {
        _toggleFilter(filter, selected);
      },
      backgroundColor: MondayDashboardConfig.hoverColor,
      selectedColor: MondayDashboardConfig.mondayBlue.withOpacity(0.1),
      checkmarkColor: MondayDashboardConfig.mondayBlue,
      labelStyle: TextStyle(
        color: isActive 
            ? MondayDashboardConfig.mondayBlue
            : MondayDashboardConfig.textSecondary,
        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isActive 
            ? MondayDashboardConfig.mondayBlue
            : MondayDashboardConfig.borderColor,
      ),
    );
  }

  /// بناء الفلاتر المتقدمة
  Widget _buildAdvancedFilters() {
    final advancedFilters = widget.availableFilters
        .where((f) => f.type != FilterType.toggle)
        .toList();

    if (advancedFilters.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'فلاتر متقدمة',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: MondayDashboardConfig.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        ...advancedFilters.map((filter) => _buildAdvancedFilter(filter)),
      ],
    );
  }

  /// بناء فلتر متقدم
  Widget _buildAdvancedFilter(DataFilter filter) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  filter.name,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: MondayDashboardConfig.textSecondary,
                  ),
                ),
              ),
              Switch(
                value: _currentFilters[filter.id]?.isActive ?? false,
                onChanged: (value) {
                  _toggleFilter(filter, value);
                },
                activeColor: MondayDashboardConfig.mondayBlue,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ],
          ),
          if (_currentFilters[filter.id]?.isActive ?? false) ...[
            const SizedBox(height: 8),
            _buildFilterInput(filter),
          ],
        ],
      ),
    );
  }

  /// بناء مدخل الفلتر
  Widget _buildFilterInput(DataFilter filter) {
    switch (filter.type) {
      case FilterType.dropdown:
        return _buildDropdownFilter(filter);
      case FilterType.multiSelect:
        return _buildMultiSelectFilter(filter);
      case FilterType.dateRange:
        return _buildDateRangeFilter(filter);
      case FilterType.slider:
        return _buildSliderFilter(filter);
      case FilterType.search:
        return _buildSearchFilter(filter);
      default:
        return const SizedBox.shrink();
    }
  }

  /// بناء فلتر القائمة المنسدلة
  Widget _buildDropdownFilter(DataFilter filter) {
    return DropdownButtonFormField<dynamic>(
      value: _currentFilters[filter.id]?.value,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.mondayBlue),
        ),
      ),
      items: filter.options?.map((option) => DropdownMenuItem(
        value: option,
        child: Text(
          option.toString(),
          style: TextStyle(
            fontSize: 14,
            color: MondayDashboardConfig.textPrimary,
          ),
        ),
      )).toList(),
      onChanged: (value) {
        _updateFilterValue(filter, value);
      },
    );
  }

  /// بناء فلتر متعدد الاختيار
  Widget _buildMultiSelectFilter(DataFilter filter) {
    final selectedValues = _currentFilters[filter.id]?.value as List<dynamic>? ?? [];
    
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: MondayDashboardConfig.borderColor),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: filter.options?.map((option) {
          final isSelected = selectedValues.contains(option);
          return CheckboxListTile(
            title: Text(
              option.toString(),
              style: TextStyle(
                fontSize: 14,
                color: MondayDashboardConfig.textPrimary,
              ),
            ),
            value: isSelected,
            onChanged: (checked) {
              final newValues = List<dynamic>.from(selectedValues);
              if (checked == true) {
                newValues.add(option);
              } else {
                newValues.remove(option);
              }
              _updateFilterValue(filter, newValues);
            },
            activeColor: MondayDashboardConfig.mondayBlue,
            dense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 8),
          );
        }).toList() ?? [],
      ),
    );
  }

  /// بناء فلتر نطاق التاريخ
  Widget _buildDateRangeFilter(DataFilter filter) {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            decoration: InputDecoration(
              labelText: 'من',
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            readOnly: true,
            onTap: () => _selectDateRange(filter),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: TextFormField(
            decoration: InputDecoration(
              labelText: 'إلى',
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            readOnly: true,
            onTap: () => _selectDateRange(filter),
          ),
        ),
      ],
    );
  }

  /// بناء فلتر شريط التمرير
  Widget _buildSliderFilter(DataFilter filter) {
    final value = (_currentFilters[filter.id]?.value as double?) ?? 0.0;
    
    return Slider(
      value: value,
      min: 0.0,
      max: 100.0,
      divisions: 100,
      label: value.round().toString(),
      onChanged: (newValue) {
        _updateFilterValue(filter, newValue);
      },
      activeColor: MondayDashboardConfig.mondayBlue,
    );
  }

  /// بناء فلتر البحث
  Widget _buildSearchFilter(DataFilter filter) {
    return TextFormField(
      decoration: InputDecoration(
        hintText: 'البحث...',
        prefixIcon: Icon(
          Icons.search,
          color: MondayDashboardConfig.textMuted,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.mondayBlue),
        ),
      ),
      onChanged: (value) {
        _updateFilterValue(filter, value);
      },
    );
  }

  /// بناء إجراءات الفلاتر
  Widget _buildFilterActions() {
    final hasActiveFilters = _currentFilters.values.any((f) => f.isActive);
    
    return Row(
      children: [
        if (hasActiveFilters) ...[
          TextButton(
            onPressed: _clearAllFilters,
            child: Text(
              'مسح الكل',
              style: TextStyle(
                color: MondayDashboardConfig.textMuted,
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
        Expanded(child: const SizedBox()),
        ElevatedButton(
          onPressed: _applyFilters,
          style: ElevatedButton.styleFrom(
            backgroundColor: MondayDashboardConfig.mondayBlue,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
          ),
          child: const Text('تطبيق'),
        ),
      ],
    );
  }

  /// تبديل حالة الفلتر
  void _toggleFilter(DataFilter filter, bool isActive) {
    setState(() {
      _currentFilters[filter.id] = filter.copyWith(isActive: isActive);
    });
  }

  /// تحديث قيمة الفلتر
  void _updateFilterValue(DataFilter filter, dynamic value) {
    setState(() {
      _currentFilters[filter.id] = filter.copyWith(
        value: value,
        isActive: true,
      );
    });
  }

  /// اختيار نطاق التاريخ
  Future<void> _selectDateRange(DataFilter filter) async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: MondayDashboardConfig.mondayBlue,
            ),
          ),
          child: child!,
        );
      },
    );

    if (dateRange != null) {
      _updateFilterValue(filter, {
        'start': dateRange.start,
        'end': dateRange.end,
      });
    }
  }

  /// مسح جميع الفلاتر
  void _clearAllFilters() {
    setState(() {
      _currentFilters.clear();
    });
    widget.onFiltersChanged(_currentFilters);
  }

  /// تطبيق الفلاتر
  void _applyFilters() {
    widget.onFiltersChanged(_currentFilters);
  }
}
