import 'package:get/get.dart';

import '../models/report_models.dart';
import '../professional_reports/models/report_template_models.dart';
import '../services/api/report_templates_api_service.dart';

/// Controller لإدارة قوالب التقارير
class ReportTemplateController extends GetxController {
  final ReportTemplatesApiService _apiService = ReportTemplatesApiService();
  
  // القوالب المحملة
  final RxList<ReportTemplate> _templates = <ReportTemplate>[].obs;
  final RxList<ReportTemplate> _defaultTemplates = <ReportTemplate>[].obs;
  final RxList<ReportTemplate> _customTemplates = <ReportTemplate>[].obs;
  
  // حالة التحميل
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  
  // Getters
  List<ReportTemplate> get templates => _templates;
  List<ReportTemplate> get defaultTemplates => _defaultTemplates;
  List<ReportTemplate> get customTemplates => _customTemplates;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;

  @override
  void onInit() {
    super.onInit();
    loadAllTemplates();
  }

  /// تحميل جميع القوالب
  Future<List<ReportTemplate>> getAllTemplates() async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';
      
      final templates = await _apiService.getAllTemplates();
      _templates.assignAll(templates);
      
      // تصنيف القوالب
      _defaultTemplates.assignAll(templates.where((t) => t.isDefault));
      _customTemplates.assignAll(templates.where((t) => t.isCustom));
      
      return templates;
    } catch (e) {
      _errorMessage.value = e.toString();
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل جميع القوالب (للاستخدام الداخلي)
  Future<void> loadAllTemplates() async {
    await getAllTemplates();
  }

  /// الحصول على قالب بالمعرف
  Future<ReportTemplate?> getTemplateById(String id) async {
    try {
      return await _apiService.getTemplateById(id);
    } catch (e) {
      _errorMessage.value = e.toString();
      return null;
    }
  }

  /// الحصول على القوالب حسب نوع التقرير
  List<ReportTemplate> getTemplatesByType(ReportType reportType) {
    return _templates.where((template) => template.reportType == reportType).toList();
  }

  /// الحصول على القوالب الافتراضية لنوع تقرير
  List<ReportTemplate> getDefaultTemplatesForType(ReportType reportType) {
    return _defaultTemplates.where((template) => template.reportType == reportType).toList();
  }

  /// إنشاء قالب جديد
  Future<ReportTemplate> createTemplate(ReportTemplate template) async {
    try {
      _isLoading.value = true;
      
      final createdTemplate = await _apiService.createTemplate(template);
      _templates.add(createdTemplate);
      
      if (createdTemplate.isCustom) {
        _customTemplates.add(createdTemplate);
      }
      
      return createdTemplate;
    } catch (e) {
      _errorMessage.value = e.toString();
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث قالب موجود
  Future<ReportTemplate> updateTemplate(ReportTemplate template) async {
    try {
      _isLoading.value = true;
      
      final updatedTemplate = await _apiService.updateTemplate(template);
      
      // تحديث القائمة المحلية
      final index = _templates.indexWhere((t) => t.id == template.id);
      if (index != -1) {
        _templates[index] = updatedTemplate;
      }
      
      // تحديث القوائم المصنفة
      if (updatedTemplate.isDefault) {
        final defaultIndex = _defaultTemplates.indexWhere((t) => t.id == template.id);
        if (defaultIndex != -1) {
          _defaultTemplates[defaultIndex] = updatedTemplate;
        }
      }
      
      if (updatedTemplate.isCustom) {
        final customIndex = _customTemplates.indexWhere((t) => t.id == template.id);
        if (customIndex != -1) {
          _customTemplates[customIndex] = updatedTemplate;
        }
      }
      
      return updatedTemplate;
    } catch (e) {
      _errorMessage.value = e.toString();
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف قالب
  Future<void> deleteTemplate(String id) async {
    try {
      _isLoading.value = true;
      
      await _apiService.deleteTemplate(id);
      
      // إزالة من القوائم المحلية
      _templates.removeWhere((t) => t.id == id);
      _defaultTemplates.removeWhere((t) => t.id == id);
      _customTemplates.removeWhere((t) => t.id == id);
      
    } catch (e) {
      _errorMessage.value = e.toString();
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// نسخ قالب موجود
  Future<ReportTemplate> duplicateTemplate(String templateId, String newName) async {
    try {
      _isLoading.value = true;
      
      final duplicatedTemplate = await _apiService.duplicateTemplate(templateId, newName);
      _templates.add(duplicatedTemplate);
      _customTemplates.add(duplicatedTemplate);
      
      return duplicatedTemplate;
    } catch (e) {
      _errorMessage.value = e.toString();
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// استعادة القوالب الافتراضية
  Future<void> resetDefaultTemplates() async {
    try {
      _isLoading.value = true;
      
      await _apiService.resetDefaultTemplates();
      
      // إعادة تحميل القوالب
      await loadAllTemplates();
      
    } catch (e) {
      _errorMessage.value = e.toString();
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تصدير قالب
  Future<String> exportTemplate(String templateId) async {
    try {
      return await _apiService.exportTemplate(templateId);
    } catch (e) {
      _errorMessage.value = e.toString();
      rethrow;
    }
  }

  /// استيراد قالب
  Future<ReportTemplate> importTemplate(String templateData) async {
    try {
      _isLoading.value = true;
      
      final importedTemplate = await _apiService.importTemplate(templateData);
      _templates.add(importedTemplate);
      _customTemplates.add(importedTemplate);
      
      return importedTemplate;
    } catch (e) {
      _errorMessage.value = e.toString();
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// إنشاء قالب من تقرير موجود
  Future<ReportTemplate> createTemplateFromReport(int reportId, String templateName) async {
    try {
      _isLoading.value = true;
      
      final template = await _apiService.createTemplateFromReport(reportId, templateName);
      _templates.add(template);
      _customTemplates.add(template);
      
      return template;
    } catch (e) {
      _errorMessage.value = e.toString();
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// البحث في القوالب
  List<ReportTemplate> searchTemplates(String query) {
    if (query.isEmpty) return _templates;
    
    return _templates.where((template) {
      return template.name.toLowerCase().contains(query.toLowerCase()) ||
             template.description.toLowerCase().contains(query.toLowerCase()) ||
             template.reportType.displayName.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// فلترة القوالب حسب معايير متعددة
  List<ReportTemplate> filterTemplates({
    ReportType? reportType,
    bool? isDefault,
    bool? isCustom,
    String? searchQuery,
  }) {
    var filtered = _templates.where((template) {
      if (reportType != null && template.reportType != reportType) {
        return false;
      }
      
      if (isDefault != null && template.isDefault != isDefault) {
        return false;
      }
      
      if (isCustom != null && template.isCustom != isCustom) {
        return false;
      }
      
      if (searchQuery != null && searchQuery.isNotEmpty) {
        if (!template.name.toLowerCase().contains(searchQuery.toLowerCase()) &&
            !template.description.toLowerCase().contains(searchQuery.toLowerCase())) {
          return false;
        }
      }
      
      return true;
    });
    
    return filtered.toList();
  }

  /// الحصول على إحصائيات القوالب
  Map<String, dynamic> getTemplateStatistics() {
    return {
      'totalTemplates': _templates.length,
      'defaultTemplates': _defaultTemplates.length,
      'customTemplates': _customTemplates.length,
      'templatesByType': _getTemplatesByTypeCount(),
    };
  }

  Map<String, int> _getTemplatesByTypeCount() {
    final Map<String, int> counts = {};
    
    for (final template in _templates) {
      final typeName = template.reportType.displayName;
      counts[typeName] = (counts[typeName] ?? 0) + 1;
    }
    
    return counts;
  }

  /// مسح رسائل الخطأ
  void clearError() {
    _errorMessage.value = '';
  }

  /// تحديث حالة التحميل
  void setLoading(bool loading) {
    _isLoading.value = loading;
  }
}
