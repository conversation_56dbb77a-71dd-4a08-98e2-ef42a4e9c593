import 'package:flutter/material.dart';
import '../config/monday_dashboard_config.dart';

/// أنواع الويدجت المدعومة
enum WidgetType {
  chart,
  numbers,
  table,
  timeline,
  workload,
  battery,
  text,
  embedded,
  files,
  form,
  calendar,
  map,
  gantt,
  kanban,
  activity,
  updates,
  notifications,
  weather,
  clock,
  countdown,
  progress,
  goals,
  metrics,
  kpi,
  funnel,
  heatmap,
  scatter,
  bubble,
  radar,
  gauge,
  speedometer,
}

/// نظام الويدجت المحسن بمعايير Monday.com
class MondayWidgetSystem {

  /// بناء ويدجت حسب النوع
  static Widget buildWidget({
    required WidgetType type,
    required Map<String, dynamic> data,
    required Size size,
    Map<String, dynamic>? settings,
    Function(String)? onAction,
  }) {
    switch (type) {
      case WidgetType.chart:
        return _buildChartWidget(data, size, settings);
      case WidgetType.numbers:
        return _buildNumbersWidget(data, size, settings);
      case WidgetType.table:
        return _buildTableWidget(data, size, settings);
      case WidgetType.timeline:
        return _buildTimelineWidget(data, size, settings);
      case WidgetType.workload:
        return _buildWorkloadWidget(data, size, settings);
      case WidgetType.battery:
        return _buildBatteryWidget(data, size, settings);
      case WidgetType.text:
        return _buildTextWidget(data, size, settings);
      case WidgetType.progress:
        return _buildProgressWidget(data, size, settings);
      case WidgetType.kpi:
        return _buildKPIWidget(data, size, settings);
      case WidgetType.gauge:
        return _buildGaugeWidget(data, size, settings);
      default:
        return _buildPlaceholderWidget(type, size);
    }
  }

  /// بناء ويدجت المخططات
  static Widget _buildChartWidget(
    Map<String, dynamic> data,
    Size size,
    Map<String, dynamic>? settings,
  ) {
    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        border: Border.all(color: MondayDashboardConfig.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWidgetHeader(
            title: data['title'] ?? 'مخطط بياني',
            subtitle: data['description'],
            actions: _buildChartActions(),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: _buildChartContent(data, settings),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ويدجت الأرقام الإحصائية
  static Widget _buildNumbersWidget(
    Map<String, dynamic> data,
    Size size,
    Map<String, dynamic>? settings,
  ) {
    final value = data['value'] ?? 0;
    final label = data['label'] ?? 'إحصائية';
    final trend = data['trend'] ?? 0.0;
    final color = data['color'] ?? MondayDashboardConfig.mondayBlue;

    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        border: Border.all(color: MondayDashboardConfig.borderColor),
      ),
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: MondayDashboardConfig.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                value.toString(),
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              if (trend != 0) ...[
                const SizedBox(width: 12),
                _buildTrendIndicator(trend),
              ],
            ],
          ),
          if (data['subtitle'] != null) ...[
            const SizedBox(height: 4),
            Text(
              data['subtitle'],
              style: TextStyle(
                fontSize: 12,
                color: MondayDashboardConfig.textMuted,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء ويدجت الجدول
  static Widget _buildTableWidget(
    Map<String, dynamic> data,
    Size size,
    Map<String, dynamic>? settings,
  ) {
    final columns = data['columns'] as List<String>? ?? [];
    final rows = data['rows'] as List<List<dynamic>>? ?? [];

    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        border: Border.all(color: MondayDashboardConfig.borderColor),
      ),
      child: Column(
        children: [
          _buildWidgetHeader(
            title: data['title'] ?? 'جدول البيانات',
            subtitle: data['description'],
          ),
          Expanded(
            child: SingleChildScrollView(
              child: DataTable(
                columns: columns.map((col) => DataColumn(
                  label: Text(
                    col,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: MondayDashboardConfig.textPrimary,
                    ),
                  ),
                )).toList(),
                rows: rows.map((row) => DataRow(
                  cells: row.map((cell) => DataCell(
                    Text(
                      cell.toString(),
                      style: TextStyle(
                        color: MondayDashboardConfig.textSecondary,
                      ),
                    ),
                  )).toList(),
                )).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ويدجت التقدم
  static Widget _buildProgressWidget(
    Map<String, dynamic> data,
    Size size,
    Map<String, dynamic>? settings,
  ) {
    final progress = (data['progress'] ?? 0.0) as double;
    final label = data['label'] ?? 'التقدم';
    final color = data['color'] ?? MondayDashboardConfig.mondayGreen;

    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        border: Border.all(color: MondayDashboardConfig.borderColor),
      ),
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: MondayDashboardConfig.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: progress / 100,
            backgroundColor: MondayDashboardConfig.borderColor,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8,
          ),
          const SizedBox(height: 8),
          Text(
            '${progress.toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ويدجت مؤشرات الأداء الرئيسية
  static Widget _buildKPIWidget(
    Map<String, dynamic> data,
    Size size,
    Map<String, dynamic>? settings,
  ) {
    final kpis = data['kpis'] as List<Map<String, dynamic>>? ?? [];

    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        border: Border.all(color: MondayDashboardConfig.borderColor),
      ),
      child: Column(
        children: [
          _buildWidgetHeader(
            title: data['title'] ?? 'مؤشرات الأداء',
            subtitle: data['description'],
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: kpis.length,
                itemBuilder: (context, index) {
                  final kpi = kpis[index];
                  return _buildKPIItem(kpi);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر مؤشر أداء واحد
  static Widget _buildKPIItem(Map<String, dynamic> kpi) {
    return Container(
      decoration: BoxDecoration(
        color: MondayDashboardConfig.hoverColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: MondayDashboardConfig.borderColor),
      ),
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            kpi['label'] ?? '',
            style: TextStyle(
              fontSize: 12,
              color: MondayDashboardConfig.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            kpi['value']?.toString() ?? '0',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: kpi['color'] ?? MondayDashboardConfig.mondayBlue,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ويدجت نائب (للأنواع غير المطبقة بعد)
  static Widget _buildPlaceholderWidget(WidgetType type, Size size) {
    return Container(
      width: size.width,
      height: size.height,
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        border: Border.all(color: MondayDashboardConfig.borderColor),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.widgets_outlined,
              size: 48,
              color: MondayDashboardConfig.textMuted,
            ),
            const SizedBox(height: 16),
            Text(
              'ويدجت ${type.name}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: MondayDashboardConfig.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'قريباً...',
              style: TextStyle(
                fontSize: 14,
                color: MondayDashboardConfig.textMuted,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رأس الويدجت
  static Widget _buildWidgetHeader({
    required String title,
    String? subtitle,
    List<Widget>? actions,
  }) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: MondayDashboardConfig.borderColor),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: MondayDashboardConfig.textPrimary,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: MondayDashboardConfig.textMuted,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (actions != null) ...actions,
        ],
      ),
    );
  }

  /// بناء مؤشر الاتجاه
  static Widget _buildTrendIndicator(double trend) {
    final isPositive = trend > 0;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isPositive 
          ? MondayDashboardConfig.mondayGreen.withOpacity(0.1)
          : MondayDashboardConfig.mondayRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPositive ? Icons.trending_up : Icons.trending_down,
            size: 16,
            color: isPositive 
              ? MondayDashboardConfig.mondayGreen
              : MondayDashboardConfig.mondayRed,
          ),
          const SizedBox(width: 4),
          Text(
            '${trend.abs().toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isPositive 
                ? MondayDashboardConfig.mondayGreen
                : MondayDashboardConfig.mondayRed,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء إجراءات المخطط
  static List<Widget> _buildChartActions() {
    return [
      IconButton(
        icon: Icon(
          Icons.more_vert,
          color: MondayDashboardConfig.textSecondary,
        ),
        onPressed: () {
          // إجراءات المخطط
        },
      ),
    ];
  }

  /// بناء محتوى المخطط (نائب)
  static Widget _buildChartContent(
    Map<String, dynamic> data,
    Map<String, dynamic>? settings,
  ) {
    return Center(
      child: Text(
        'محتوى المخطط',
        style: TextStyle(
          color: MondayDashboardConfig.textMuted,
        ),
      ),
    );
  }

  // إضافة الويدجت المفقودة...
  static Widget _buildTimelineWidget(Map<String, dynamic> data, Size size, Map<String, dynamic>? settings) => _buildPlaceholderWidget(WidgetType.timeline, size);
  static Widget _buildWorkloadWidget(Map<String, dynamic> data, Size size, Map<String, dynamic>? settings) => _buildPlaceholderWidget(WidgetType.workload, size);
  static Widget _buildBatteryWidget(Map<String, dynamic> data, Size size, Map<String, dynamic>? settings) => _buildPlaceholderWidget(WidgetType.battery, size);
  static Widget _buildTextWidget(Map<String, dynamic> data, Size size, Map<String, dynamic>? settings) => _buildPlaceholderWidget(WidgetType.text, size);
  static Widget _buildGaugeWidget(Map<String, dynamic> data, Size size, Map<String, dynamic>? settings) => _buildPlaceholderWidget(WidgetType.gauge, size);
}
