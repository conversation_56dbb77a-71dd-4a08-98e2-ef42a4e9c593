import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/dashboard_models.dart';
import '../config/monday_dashboard_config.dart';
import 'monday_widget_system.dart';

/// نظام الشبكة المتقدم بمعايير Monday.com
class MondayGridSystem extends StatefulWidget {
  final List<ChartCardModel> cards;
  final Function(List<ChartCardModel>)? onCardsReordered;
  final Function(ChartCardModel)? onCardUpdated;
  final Function()? onRefresh;
  final Function(String)? onRefreshSingle;
  final bool isEditMode;
  final int columns;
  final double spacing;

  const MondayGridSystem({
    super.key,
    required this.cards,
    this.onCardsReordered,
    this.onCardUpdated,
    this.onRefresh,
    this.onRefreshSingle,
    this.isEditMode = false,
    this.columns = 2,
    this.spacing = 20.0,
  });

  @override
  State<MondayGridSystem> createState() => _MondayGridSystemState();
}

class _MondayGridSystemState extends State<MondayGridSystem>
    with TickerProviderStateMixin {
  late List<ChartCardModel> _cards;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  String? _draggedCardId;
  int? _dragTargetIndex;

  @override
  void initState() {
    super.initState();
    _cards = List.from(widget.cards);
    
    _animationController = AnimationController(
      duration: MondayDashboardConfig.animationDuration,
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: MondayDashboardConfig.animationCurve,
    ));
    
    _animationController.forward();
  }

  @override
  void didUpdateWidget(MondayGridSystem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.cards != oldWidget.cards) {
      setState(() {
        _cards = List.from(widget.cards);
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        color: MondayDashboardConfig.backgroundColor,
        child: _buildGridLayout(),
      ),
    );
  }

  /// بناء تخطيط الشبكة
  Widget _buildGridLayout() {
    if (_cards.isEmpty) {
      return _buildEmptyState();
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(widget.spacing),
      child: _buildResponsiveGrid(),
    );
  }

  /// بناء الشبكة المتجاوبة
  Widget _buildResponsiveGrid() {
    final screenWidth = MediaQuery.of(context).size.width;
    final effectiveColumns = _calculateColumns(screenWidth);
    
    return Wrap(
      spacing: widget.spacing,
      runSpacing: widget.spacing,
      children: _cards.asMap().entries.map((entry) {
        final index = entry.key;
        final card = entry.value;
        
        return _buildGridItem(card, index, effectiveColumns, screenWidth);
      }).toList(),
    );
  }

  /// حساب عدد الأعمدة بناءً على عرض الشاشة
  int _calculateColumns(double screenWidth) {
    if (screenWidth < 768) return 1;      // موبايل
    if (screenWidth < 1024) return 2;     // تابلت
    if (screenWidth < 1440) return widget.columns; // سطح المكتب الصغير
    return widget.columns + 1;            // سطح المكتب الكبير
  }

  /// بناء عنصر الشبكة
  Widget _buildGridItem(
    ChartCardModel card,
    int index,
    int columns,
    double screenWidth,
  ) {
    final itemWidth = _calculateItemWidth(screenWidth, columns);
    
    return AnimatedContainer(
      duration: MondayDashboardConfig.animationDuration,
      curve: MondayDashboardConfig.animationCurve,
      width: itemWidth,
      child: widget.isEditMode
          ? _buildEditableCard(card, index)
          : _buildCard(card, index),
    );
  }

  /// حساب عرض العنصر
  double _calculateItemWidth(double screenWidth, int columns) {
    final totalSpacing = widget.spacing * (columns + 1);
    return (screenWidth - totalSpacing) / columns;
  }

  /// بناء البطاقة القابلة للتحرير
  Widget _buildEditableCard(ChartCardModel card, int index) {
    return Draggable<ChartCardModel>(
      data: card,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        child: Container(
          width: card.size.width * 0.8,
          height: card.size.height * 0.8,
          decoration: BoxDecoration(
            color: MondayDashboardConfig.cardBackground,
            borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
            border: Border.all(
              color: MondayDashboardConfig.mondayBlue,
              width: 2,
            ),
          ),
          child: _buildCardContent(card, isDragging: true),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.5,
        child: _buildCard(card, index),
      ),
      onDragStarted: () {
        setState(() {
          _draggedCardId = card.id;
        });
        HapticFeedback.lightImpact();
      },
      onDragEnd: (details) {
        setState(() {
          _draggedCardId = null;
          _dragTargetIndex = null;
        });
      },
      child: DragTarget<ChartCardModel>(
        onWillAccept: (data) => data != null && data.id != card.id,
        onAccept: (draggedCard) {
          _reorderCards(draggedCard, index);
        },
        onMove: (details) {
          setState(() {
            _dragTargetIndex = index;
          });
        },
        onLeave: (data) {
          setState(() {
            _dragTargetIndex = null;
          });
        },
        builder: (context, candidateData, rejectedData) {
          final isDropTarget = _dragTargetIndex == index && 
                              _draggedCardId != null && 
                              _draggedCardId != card.id;
          
          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
              border: isDropTarget
                  ? Border.all(
                      color: MondayDashboardConfig.mondayBlue,
                      width: 2,
                    )
                  : null,
            ),
            child: _buildCard(card, index),
          );
        },
      ),
    );
  }

  /// بناء البطاقة العادية
  Widget _buildCard(ChartCardModel card, int index) {
    return MouseRegion(
      onEnter: (_) => _onCardHover(card.id, true),
      onExit: (_) => _onCardHover(card.id, false),
      child: AnimatedContainer(
        duration: MondayDashboardConfig.hoverAnimationDuration,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: _buildCardContent(card),
      ),
    );
  }

  /// بناء محتوى البطاقة
  Widget _buildCardContent(ChartCardModel card, {bool isDragging = false}) {
    return Container(
      width: card.size.width,
      height: card.size.height,
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        border: Border.all(
          color: isDragging 
              ? MondayDashboardConfig.mondayBlue
              : MondayDashboardConfig.borderColor,
          width: isDragging ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCardHeader(card),
          Expanded(
            child: _buildCardBody(card),
          ),
          if (widget.isEditMode) _buildCardFooter(card),
        ],
      ),
    );
  }

  /// بناء رأس البطاقة
  Widget _buildCardHeader(ChartCardModel card) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: MondayDashboardConfig.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  card.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: MondayDashboardConfig.textPrimary,
                  ),
                ),
                if (card.description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    card.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: MondayDashboardConfig.textMuted,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          _buildCardActions(card),
        ],
      ),
    );
  }

  /// بناء جسم البطاقة
  Widget _buildCardBody(ChartCardModel card) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: MondayWidgetSystem.buildWidget(
        type: MondayWidgetSystem.WidgetType.chart,
        data: {
          'title': card.title,
          'description': card.description,
          'data': card.data,
          'chartType': card.chartType,
        },
        size: Size(
          card.size.width - 32,
          card.size.height - 120,
        ),
      ),
    );
  }

  /// بناء إجراءات البطاقة
  Widget _buildCardActions(ChartCardModel card) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: MondayDashboardConfig.textSecondary,
        size: 20,
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'refresh',
          child: Row(
            children: [
              Icon(Icons.refresh, size: 16),
              const SizedBox(width: 8),
              Text('تحديث'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 16),
              const SizedBox(width: 8),
              Text('تحرير'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'fullscreen',
          child: Row(
            children: [
              Icon(Icons.fullscreen, size: 16),
              const SizedBox(width: 8),
              Text('ملء الشاشة'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'export',
          child: Row(
            children: [
              Icon(Icons.download, size: 16),
              const SizedBox(width: 8),
              Text('تصدير'),
            ],
          ),
        ),
      ],
      onSelected: (value) => _handleCardAction(card, value),
    );
  }

  /// بناء تذييل البطاقة (في وضع التحرير)
  Widget _buildCardFooter(ChartCardModel card) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: MondayDashboardConfig.hoverColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(MondayDashboardConfig.cardBorderRadius),
          bottomRight: Radius.circular(MondayDashboardConfig.cardBorderRadius),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.drag_indicator,
            color: MondayDashboardConfig.textMuted,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'اسحب لإعادة الترتيب',
            style: TextStyle(
              fontSize: 12,
              color: MondayDashboardConfig.textMuted,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.dashboard_outlined,
            size: 64,
            color: MondayDashboardConfig.textMuted,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد بطاقات للعرض',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: MondayDashboardConfig.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف بطاقات جديدة لبدء استخدام لوحة التحكم',
            style: TextStyle(
              fontSize: 14,
              color: MondayDashboardConfig.textMuted,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // إضافة بطاقة جديدة
            },
            icon: const Icon(Icons.add),
            label: const Text('إضافة بطاقة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: MondayDashboardConfig.mondayBlue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// معالجة تحويم البطاقة
  void _onCardHover(String cardId, bool isHovering) {
    // يمكن إضافة تأثيرات التحويم هنا
  }

  /// إعادة ترتيب البطاقات
  void _reorderCards(ChartCardModel draggedCard, int targetIndex) {
    final draggedIndex = _cards.indexWhere((card) => card.id == draggedCard.id);
    if (draggedIndex == -1 || draggedIndex == targetIndex) return;

    setState(() {
      _cards.removeAt(draggedIndex);
      _cards.insert(targetIndex, draggedCard);
    });

    widget.onCardsReordered?.call(_cards);
    HapticFeedback.mediumImpact();
  }

  /// معالجة إجراءات البطاقة
  void _handleCardAction(ChartCardModel card, String action) {
    switch (action) {
      case 'refresh':
        widget.onRefreshSingle?.call(card.id);
        break;
      case 'edit':
        _showCardEditDialog(card);
        break;
      case 'fullscreen':
        _showCardFullscreen(card);
        break;
      case 'export':
        _exportCard(card);
        break;
    }
  }

  /// عرض حوار تحرير البطاقة
  void _showCardEditDialog(ChartCardModel card) {
    // تنفيذ حوار التحرير
  }

  /// عرض البطاقة بملء الشاشة
  void _showCardFullscreen(ChartCardModel card) {
    // تنفيذ العرض بملء الشاشة
  }

  /// تصدير البطاقة
  void _exportCard(ChartCardModel card) {
    // تنفيذ التصدير
  }
}
