using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة جدولة التقارير
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class ReportSchedulesController(TasksDbContext context) : ControllerBase
    {
        private readonly TasksDbContext _context = context;

        /// <summary>
        /// الحصول على جميع جدولة التقارير
        /// </summary>
        /// <returns>قائمة بجميع جدولة التقارير</returns>
        /// <response code="200">إرجاع قائمة جدولة التقارير</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ReportSchedule>>> GetReportSchedules()
        {
            return await _context.ReportSchedules
                .Include(rs => rs.Report)
                .Include(rs => rs.CreatedByNavigation)
                .OrderByDescending(rs => rs.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على جدولة تقرير محددة
        /// </summary>
        /// <param name="id">معرف جدولة التقرير</param>
        /// <returns>جدولة التقرير المطلوبة</returns>
        /// <response code="200">إرجاع جدولة التقرير</response>
        /// <response code="404">جدولة التقرير غير موجودة</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ReportSchedule>> GetReportSchedule(int id)
        {
            var reportSchedule = await _context.ReportSchedules
                .Include(rs => rs.Report)
                .Include(rs => rs.CreatedByNavigation)
                .FirstOrDefaultAsync(rs => rs.Id == id);

            if (reportSchedule == null)
            {
                return NotFound();
            }

            return reportSchedule;
        }

        /// <summary>
        /// الحصول على جدولة التقارير لتقرير محدد
        /// </summary>
        /// <param name="reportId">معرف التقرير</param>
        /// <returns>قائمة جدولة التقارير للتقرير</returns>
        /// <response code="200">إرجاع قائمة جدولة التقارير</response>
        [HttpGet("report/{reportId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ReportSchedule>>> GetReportSchedulesByReport(int reportId)
        {
            return await _context.ReportSchedules
                .Include(rs => rs.CreatedByNavigation)
                .Where(rs => rs.ReportId == reportId)
                .OrderByDescending(rs => rs.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الجدولة النشطة
        /// </summary>
        /// <returns>قائمة الجدولة النشطة</returns>
        /// <response code="200">إرجاع قائمة الجدولة النشطة</response>
        [HttpGet("active")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ReportSchedule>>> GetActiveSchedules()
        {
            return await _context.ReportSchedules
                .Include(rs => rs.Report)
                .Include(rs => rs.CreatedByNavigation)
                .Where(rs => rs.IsActive)
                .OrderBy(rs => rs.NextExecutionAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الجدولة المستحقة للتنفيذ
        /// </summary>
        /// <returns>قائمة الجدولة المستحقة</returns>
        /// <response code="200">إرجاع قائمة الجدولة المستحقة</response>
        [HttpGet("due")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ReportSchedule>>> GetDueSchedules()
        {
            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            return await _context.ReportSchedules
                .Include(rs => rs.Report)
                .Include(rs => rs.CreatedByNavigation)
                .Where(rs => rs.IsActive &&
                           rs.NextExecutionAt <= currentTime)
                .OrderBy(rs => rs.NextExecutionAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على جدولة التقارير حسب التكرار
        /// </summary>
        /// <param name="frequency">تكرار التقرير</param>
        /// <returns>قائمة جدولة التقارير بالتكرار المحدد</returns>
        /// <response code="200">إرجاع قائمة جدولة التقارير</response>
        [HttpGet("frequency/{frequency}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ReportSchedule>>> GetSchedulesByFrequency(string frequency)
        {
            return await _context.ReportSchedules
                .Include(rs => rs.Report)
                .Include(rs => rs.CreatedByNavigation)
                .Where(rs => rs.Frequency == frequency)
                .OrderByDescending(rs => rs.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// إحصائيات جدولة التقارير
        /// </summary>
        /// <returns>إحصائيات الجدولة</returns>
        /// <response code="200">إرجاع إحصائيات الجدولة</response>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetScheduleStatistics()
        {
            var totalSchedules = await _context.ReportSchedules.CountAsync();
            var activeSchedules = await _context.ReportSchedules.CountAsync(rs => rs.IsActive);
            var inactiveSchedules = totalSchedules - activeSchedules;

            var schedulesByFrequency = await _context.ReportSchedules
                .GroupBy(rs => rs.Frequency)
                .Select(g => new { Frequency = g.Key, Count = g.Count() })
                .ToListAsync();

            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var dueSchedules = await _context.ReportSchedules
                .CountAsync(rs => rs.IsActive &&
                                rs.NextExecutionAt <= currentTime);

            return Ok(new
            {
                TotalSchedules = totalSchedules,
                ActiveSchedules = activeSchedules,
                InactiveSchedules = inactiveSchedules,
                DueSchedules = dueSchedules,
                SchedulesByFrequency = schedulesByFrequency
            });
        }

        /// <summary>
        /// إنشاء جدولة تقرير جديدة
        /// </summary>
        /// <param name="reportSchedule">بيانات جدولة التقرير</param>
        /// <returns>جدولة التقرير المُنشأة</returns>
        /// <response code="201">تم إنشاء جدولة التقرير بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ReportSchedule>> PostReportSchedule(ReportSchedule reportSchedule)
        {
            // التحقق من وجود التقرير
            var reportExists = await _context.Reports.AnyAsync(r => r.Id == reportSchedule.ReportId && !r.IsDeleted);
            if (!reportExists)
            {
                return BadRequest("التقرير غير موجود");
            }

            reportSchedule.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            reportSchedule.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            // حساب وقت التشغيل التالي إذا لم يكن محدد
            if (reportSchedule.NextExecutionAt == 0 && !string.IsNullOrEmpty(reportSchedule.Frequency))
            {
                reportSchedule.NextExecutionAt = CalculateNextRunTime(reportSchedule.Frequency);
            }

            _context.ReportSchedules.Add(reportSchedule);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetReportSchedule", new { id = reportSchedule.Id }, reportSchedule);
        }

        /// <summary>
        /// تحديث جدولة تقرير
        /// </summary>
        /// <param name="id">معرف جدولة التقرير</param>
        /// <param name="reportSchedule">بيانات جدولة التقرير المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث جدولة التقرير بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">جدولة التقرير غير موجودة</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutReportSchedule(int id, ReportSchedule reportSchedule)
        {
            if (id != reportSchedule.Id)
            {
                return BadRequest();
            }

            reportSchedule.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            _context.Entry(reportSchedule).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ReportScheduleExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// تفعيل/إلغاء تفعيل جدولة تقرير
        /// </summary>
        /// <param name="id">معرف جدولة التقرير</param>
        /// <param name="active">حالة التفعيل</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث حالة الجدولة بنجاح</response>
        /// <response code="404">جدولة التقرير غير موجودة</response>
        [HttpPatch("{id}/toggle-active")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ToggleActive(int id, [FromQuery] bool active)
        {
            var reportSchedule = await _context.ReportSchedules.FindAsync(id);
            if (reportSchedule == null)
            {
                return NotFound();
            }

            reportSchedule.IsActive = active;
            reportSchedule.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// تحديث وقت التشغيل التالي
        /// </summary>
        /// <param name="id">معرف جدولة التقرير</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث وقت التشغيل التالي بنجاح</response>
        /// <response code="404">جدولة التقرير غير موجودة</response>
        [HttpPatch("{id}/update-next-run")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateNextRunTime(int id)
        {
            var reportSchedule = await _context.ReportSchedules.FindAsync(id);
            if (reportSchedule == null)
            {
                return NotFound();
            }

            if (!string.IsNullOrEmpty(reportSchedule.Frequency))
            {
                reportSchedule.NextExecutionAt = CalculateNextRunTime(reportSchedule.Frequency);
                reportSchedule.LastExecutionAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                reportSchedule.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();
            }

            return NoContent();
        }

        /// <summary>
        /// حذف جدولة تقرير (حذف فعلي)
        /// </summary>
        /// <param name="id">معرف جدولة التقرير</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف جدولة التقرير بنجاح</response>
        /// <response code="404">جدولة التقرير غير موجودة</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteReportSchedule(int id)
        {
            var reportSchedule = await _context.ReportSchedules.FindAsync(id);
            if (reportSchedule == null)
            {
                return NotFound();
            }

            // حذف فعلي من قاعدة البيانات
            _context.ReportSchedules.Remove(reportSchedule);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// حساب وقت التشغيل التالي بناءً على التكرار
        /// </summary>
        /// <param name="frequency">تكرار التقرير</param>
        /// <returns>وقت التشغيل التالي</returns>
        private static long CalculateNextRunTime(string frequency)
        {
            var now = DateTimeOffset.UtcNow;

            return frequency.ToLower() switch
            {
                "daily" => now.AddDays(1).ToUnixTimeSeconds(),
                "weekly" => now.AddDays(7).ToUnixTimeSeconds(),
                "monthly" => now.AddMonths(1).ToUnixTimeSeconds(),
                "quarterly" => now.AddMonths(3).ToUnixTimeSeconds(),
                "yearly" => now.AddYears(1).ToUnixTimeSeconds(),
                _ => now.AddDays(1).ToUnixTimeSeconds() // افتراضي يومي
            };
        }

        /// <summary>
        /// الحصول على إحصائيات الجدولة المفصلة
        /// </summary>
        /// <returns>إحصائيات الجدولة المفصلة</returns>
        /// <response code="200">إرجاع إحصائيات الجدولة المفصلة</response>
        [HttpGet("detailed-statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetDetailedScheduleStatistics()
        {
            var totalSchedules = await _context.ReportSchedules.CountAsync();
            var activeSchedules = await _context.ReportSchedules.CountAsync(rs => rs.IsActive);
            var inactiveSchedules = totalSchedules - activeSchedules;

            var schedulesByFrequency = await _context.ReportSchedules
                .GroupBy(rs => rs.Frequency)
                .Select(g => new { Frequency = g.Key, Count = g.Count() })
                .ToListAsync();

            var todayStart = new DateTimeOffset(DateTimeOffset.UtcNow.Date).ToUnixTimeSeconds();
            var schedulesExecutedToday = await _context.ReportSchedules
                .Where(rs => rs.LastExecutionAt.HasValue &&
                           rs.LastExecutionAt >= todayStart)
                .CountAsync();

            var nextWeek = DateTimeOffset.UtcNow.AddDays(7).ToUnixTimeSeconds();
            var upcomingSchedules = await _context.ReportSchedules
                .Where(rs => rs.IsActive && rs.NextExecutionAt <= nextWeek)
                .CountAsync();

            return Ok(new
            {
                TotalSchedules = totalSchedules,
                ActiveSchedules = activeSchedules,
                InactiveSchedules = inactiveSchedules,
                SchedulesByFrequency = schedulesByFrequency,
                SchedulesExecutedToday = schedulesExecutedToday,
                UpcomingSchedules = upcomingSchedules
            });
        }

        /// <summary>
        /// تنفيذ جدولة تقرير فوراً
        /// </summary>
        /// <param name="id">معرف جدولة التقرير</param>
        /// <returns>نتيجة التنفيذ</returns>
        /// <response code="200">تم التنفيذ بنجاح</response>
        /// <response code="404">جدولة التقرير غير موجودة</response>
        [HttpPost("{id}/execute")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> ExecuteSchedule(int id)
        {
            var schedule = await _context.ReportSchedules
                .Include(rs => rs.Report)
                .FirstOrDefaultAsync(rs => rs.Id == id);

            if (schedule == null)
            {
                return NotFound();
            }

            try
            {
                // تحديث وقت التنفيذ الأخير
                schedule.LastExecutionAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // حساب وقت التنفيذ التالي
                schedule.NextExecutionAt = CalculateNextRunTime(schedule.Frequency);

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    message = "تم تنفيذ الجدولة بنجاح",
                    executedAt = schedule.LastExecutionAt,
                    nextExecutionAt = schedule.NextExecutionAt
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "خطأ في تنفيذ الجدولة", error = ex.Message });
            }
        }

        /// <summary>
        /// تفعيل أو إلغاء تفعيل جدولة تقرير
        /// </summary>
        /// <param name="id">معرف جدولة التقرير</param>
        /// <param name="isActive">حالة التفعيل</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم التحديث بنجاح</response>
        /// <response code="404">جدولة التقرير غير موجودة</response>
        [HttpPatch("{id}/toggle")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ToggleSchedule(int id, [FromBody] bool isActive)
        {
            var schedule = await _context.ReportSchedules.FindAsync(id);
            if (schedule == null)
            {
                return NotFound();
            }

            schedule.IsActive = isActive;

            // إذا تم تفعيل الجدولة، احسب وقت التنفيذ التالي
            if (isActive)
            {
                schedule.NextExecutionAt = CalculateNextRunTime(schedule.Frequency);
            }

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// الحصول على سجل تنفيذ الجدولة
        /// </summary>
        /// <param name="id">معرف جدولة التقرير</param>
        /// <param name="limit">عدد السجلات المطلوبة</param>
        /// <returns>سجل التنفيذ</returns>
        /// <response code="200">إرجاع سجل التنفيذ</response>
        /// <response code="404">جدولة التقرير غير موجودة</response>
        [HttpGet("{id}/execution-history")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> GetExecutionHistory(int id, [FromQuery] int limit = 50)
        {
            var schedule = await _context.ReportSchedules.FindAsync(id);
            if (schedule == null)
            {
                return NotFound();
            }

            // هنا يمكن إضافة جدول منفصل لسجل التنفيذ
            // حالياً سنعيد معلومات أساسية
            return Ok(new
            {
                ScheduleId = id,
                LastExecutionAt = schedule.LastExecutionAt,
                NextExecutionAt = schedule.NextExecutionAt,
                IsActive = schedule.IsActive,
                Frequency = schedule.Frequency,
                // يمكن إضافة المزيد من التفاصيل هنا
            });
        }

        private bool ReportScheduleExists(int id)
        {
            return _context.ReportSchedules.Any(e => e.Id == id);
        }
    }
}
