import 'package:flutter/material.dart';
import '../models/dashboard_models.dart';

/// إعدادات لوحة التحكم المحسنة بمعايير Monday.com
class MondayDashboardConfig {
  // ألوان Monday.com الرسمية
  static const Color mondayBlue = Color(0xFF0073EA);
  static const Color mondayPurple = Color(0xFF401694);
  static const Color mondayGreen = Color(0xFF00C875);
  static const Color mondayOrange = Color(0xFFFF642E);
  static const Color mondayRed = Color(0xFFE2445C);
  static const Color mondayYellow = Color(0xFFFFCB00);
  static const Color mondayPink = Color(0xFFFF158A);
  static const Color mondayTeal = Color(0xFF00D2D2);
  static const Color mondayIndigo = Color(0xFF5559DF);
  static const Color mondayLime = Color(0xFF9CD326);
  
  // ألوان النظام المحسنة
  static const Color primaryColor = mondayBlue;
  static const Color secondaryColor = mondayPurple;
  static const Color accentColor = mondayGreen;
  static const Color warningColor = mondayYellow;
  static const Color errorColor = mondayRed;
  static const Color successColor = mondayGreen;
  
  // ألوان الخلفية والواجهة (Monday.com style)
  static const Color backgroundColor = Color(0xFFF6F7FB);
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color borderColor = Color(0xFFD0D4E4);
  static const Color hoverColor = Color(0xFFF5F6FA);
  static const Color selectedColor = Color(0xFFE6F3FF);
  
  // ألوان النصوص
  static const Color textPrimary = Color(0xFF323338);
  static const Color textSecondary = Color(0xFF676879);
  static const Color textMuted = Color(0xFF9699A6);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // أحجام البطاقات (Monday.com style)
  static const Size smallCardSize = Size(280, 240);
  static const Size mediumCardSize = Size(380, 280);
  static const Size defaultCardSize = Size(380, 320);
  static const Size largeCardSize = Size(480, 360);
  static const Size extraLargeCardSize = Size(580, 420);
  static const Size fullWidthCardSize = Size(780, 320);

  // إعدادات الشبكة المحسنة
  static const int defaultColumns = 2;
  static const double defaultSpacing = 20.0;
  static const double cardBorderRadius = 8.0;
  static const double cardElevation = 1.0;
  static const double cardPadding = 20.0;

  // إعدادات الرسوم المتحركة
  static const Duration animationDuration = Duration(milliseconds: 250);
  static const Duration hoverAnimationDuration = Duration(milliseconds: 150);
  static const Curve animationCurve = Curves.easeInOut;

  /// أنواع الويدجت المدعومة (Monday.com compatible)
  static const List<String> supportedWidgetTypes = [
    'chart',           // مخططات بيانية
    'numbers',         // أرقام إحصائية
    'table',           // جداول
    'timeline',        // خط زمني
    'workload',        // عبء العمل
    'battery',         // مؤشر البطارية
    'text',            // نص
    'embedded',        // محتوى مدمج
    'files',           // ملفات
    'form',            // نماذج
    'calendar',        // تقويم
    'map',             // خريطة
    'gantt',           // جانت
    'kanban',          // كانبان
    'activity',        // نشاط
    'updates',         // تحديثات
    'notifications',   // إشعارات
    'weather',         // طقس
    'clock',           // ساعة
    'countdown',       // عد تنازلي
    'progress',        // تقدم
    'goals',           // أهداف
    'metrics',         // مقاييس
    'kpi',             // مؤشرات أداء
    'funnel',          // قمع
    'heatmap',         // خريطة حرارية
    'scatter',         // نقطي
    'bubble',          // فقاعي
    'radar',           // رادار
    'gauge',           // مقياس
    'speedometer',     // عداد سرعة
  ];

  /// ألوان الحالات (Monday.com style)
  static const Map<String, Color> statusColors = {
    'مكتملة': mondayGreen,
    'قيد التنفيذ': mondayBlue,
    'معلقة': mondayYellow,
    'متأخرة': mondayRed,
    'ملغاة': Color(0xFF9699A6),
    'في انتظار المراجعة': mondayPurple,
    'جاهزة للبدء': mondayTeal,
    'تحت الاختبار': mondayOrange,
    'completed': mondayGreen,
    'in_progress': mondayBlue,
    'pending': mondayYellow,
    'overdue': mondayRed,
    'cancelled': Color(0xFF9699A6),
  };

  /// ألوان الأولويات (Monday.com style)
  static const Map<String, Color> priorityColors = {
    'عالية': mondayRed,
    'متوسطة': mondayOrange,
    'منخفضة': mondayGreen,
    'حرجة': mondayPurple,
    'عاجلة': Color(0xFF8B1538),
    'high': mondayRed,
    'medium': mondayOrange,
    'low': mondayGreen,
    'critical': mondayPurple,
    'urgent': Color(0xFF8B1538),
  };

  /// مجموعة ألوان Monday.com الكاملة
  static const List<Color> mondayColorPalette = [
    mondayBlue,
    mondayPurple,
    mondayGreen,
    mondayOrange,
    mondayRed,
    mondayYellow,
    mondayPink,
    mondayTeal,
    mondayIndigo,
    mondayLime,
    Color(0xFF037F4C),  // Dark Green
    Color(0xFF784BD1),  // Purple Variant
    Color(0xFF225091),  // Dark Blue
    Color(0xFFBB3354),  // Dark Red
    Color(0xFF9D99B9),  // Light Purple
    Color(0xFF7E3B8A),  // Magenta
  ];

  /// الحصول على البطاقات الافتراضية المحسنة (Monday.com style)
  static List<ChartCardModel> getDefaultChartCards() {
    return [
      // بطاقة نظرة عامة على المهام
      ChartCardModel(
        id: 'tasks_overview',
        title: 'نظرة عامة على المهام',
        description: 'توزيع شامل للمهام حسب الحالة',
        chartType: ChartType.pie,
        data: [],
        supportedChartTypes: [
          ChartType.pie,
          ChartType.doughnut,
          ChartType.column,
          ChartType.bar,
          ChartType.stackedColumn,
        ],
        dataSource: 'tasks_status',
        size: defaultCardSize,
        colorMapping: statusColors,
      ),
      
      // بطاقة توزيع الأولويات
      ChartCardModel(
        id: 'priority_breakdown',
        title: 'توزيع الأولويات',
        description: 'تحليل المهام حسب مستوى الأولوية',
        chartType: ChartType.column,
        data: [],
        supportedChartTypes: [
          ChartType.column,
          ChartType.bar,
          ChartType.pie,
          ChartType.stackedColumn,
          ChartType.funnel,
        ],
        dataSource: 'tasks_priority',
        size: defaultCardSize,
        colorMapping: priorityColors,
      ),
      
      // بطاقة أداء الفريق
      ChartCardModel(
        id: 'team_performance',
        title: 'أداء الفريق',
        description: 'تحليل أداء أعضاء الفريق ومعدل الإنجاز',
        chartType: ChartType.bar,
        data: [],
        supportedChartTypes: [
          ChartType.bar,
          ChartType.column,
          ChartType.doughnut,
          ChartType.stackedBar,
          ChartType.radialBar,
        ],
        dataSource: 'tasks_assignee',
        size: largeCardSize,
      ),
      
      // بطاقة عبء العمل حسب القسم
      ChartCardModel(
        id: 'department_workload',
        title: 'عبء العمل حسب القسم',
        description: 'توزيع المهام والعبء على الأقسام',
        chartType: ChartType.stackedColumn,
        data: [],
        supportedChartTypes: [
          ChartType.stackedColumn,
          ChartType.stackedBar,
          ChartType.column,
          ChartType.bar,
          ChartType.pie,
        ],
        dataSource: 'tasks_department',
        size: fullWidthCardSize,
      ),
      
      // بطاقة الاتجاهات الزمنية
      ChartCardModel(
        id: 'timeline_trends',
        title: 'اتجاهات الأداء',
        description: 'تتبع الأداء والتقدم عبر الزمن',
        chartType: ChartType.line,
        data: [],
        supportedChartTypes: [
          ChartType.line,
          ChartType.spline,
          ChartType.area,
          ChartType.stepLine,
          ChartType.column,
        ],
        dataSource: 'task_trends',
        size: fullWidthCardSize,
      ),
      
      // بطاقة مصفوفة المساهمين
      ChartCardModel(
        id: 'contributor_matrix',
        title: 'مصفوفة المساهمين',
        description: 'تحليل مساهمات الفريق في المشاريع',
        chartType: ChartType.bubble,
        data: [],
        supportedChartTypes: [
          ChartType.bubble,
          ChartType.scatter,
          ChartType.column,
          ChartType.bar,
        ],
        dataSource: 'contributor_matrix',
        size: extraLargeCardSize,
      ),
    ];
  }

  /// الحصول على التخطيطات الافتراضية (Monday.com style)
  static List<DashboardLayout> getDefaultLayouts() {
    final defaultCards = getDefaultChartCards();
    
    return [
      DashboardLayout(
        id: 'monday_default',
        name: 'التخطيط الافتراضي',
        cards: defaultCards,
        columns: 2,
        cardSpacing: defaultSpacing,
        isDefault: true,
      ),
      DashboardLayout(
        id: 'monday_compact',
        name: 'العرض المضغوط',
        cards: defaultCards.map((card) => 
          card.copyWith(size: mediumCardSize)).toList(),
        columns: 3,
        cardSpacing: 16.0,
      ),
      DashboardLayout(
        id: 'monday_executive',
        name: 'عرض تنفيذي',
        cards: defaultCards.take(4).map((card) => 
          card.copyWith(size: largeCardSize)).toList(),
        columns: 2,
        cardSpacing: 24.0,
      ),
    ];
  }

  /// إعدادات لوحة التحكم الافتراضية (Monday.com style)
  static DashboardSettings getDefaultSettings() {
    return const DashboardSettings(
      autoRefresh: true,
      refreshInterval: Duration(minutes: 5),
      showGridLines: false,
      enableAnimations: true,
      theme: 'monday',
      customSettings: {
        'enableHover': true,
        'enableTooltips': true,
        'enableDataLabels': true,
        'enableLegend': true,
        'enableZoom': true,
        'enableExport': true,
        'enableFullscreen': true,
        'enableFilters': true,
        'enableColorCustomization': true,
        'enableReordering': true,
        'enableResizing': true,
      },
    );
  }
}
