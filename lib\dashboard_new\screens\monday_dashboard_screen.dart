import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/monday_dashboard_controller.dart';
import '../config/monday_dashboard_config.dart';
import '../widgets/monday_grid_system.dart';
import '../widgets/monday_filter_system.dart';
import '../widgets/monday_search_system.dart';

/// شاشة لوحة التحكم المحسنة بمعايير Monday.com
class MondayDashboardScreen extends StatefulWidget {
  const MondayDashboardScreen({super.key});

  @override
  State<MondayDashboardScreen> createState() => _MondayDashboardScreenState();
}

class _MondayDashboardScreenState extends State<MondayDashboardScreen>
    with TickerProviderStateMixin {
  late final MondayDashboardController _controller;
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _controller = Get.put(MondayDashboardController());
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MondayDashboardConfig.backgroundColor,
      appBar: _buildAppBar(),
      body: Obx(() => Column(
        children: [
          _buildTabBar(),
          _buildSearchSection(),
          if (_controller.showFilters) _buildFiltersSection(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildAnalyticsTab(),
                _buildReportsTab(),
                _buildSettingsTab(),
              ],
            ),
          ),
        ],
      )),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق المحسن
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: MondayDashboardConfig.cardBackground,
      elevation: 0,
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: MondayDashboardConfig.mondayBlue,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.dashboard,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'لوحة التحكم',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: MondayDashboardConfig.textPrimary,
                ),
              ),
              Text(
                'نظرة شاملة على الأداء',
                style: TextStyle(
                  fontSize: 12,
                  color: MondayDashboardConfig.textMuted,
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        // زر الفلاتر
        Obx(() => IconButton(
          icon: Icon(
            _controller.showFilters ? Icons.filter_list_off : Icons.filter_list,
            color: _controller.showFilters
                ? MondayDashboardConfig.mondayBlue
                : MondayDashboardConfig.textSecondary,
          ),
          onPressed: _controller.toggleFilters,
          tooltip: _controller.showFilters ? 'إخفاء الفلاتر' : 'إظهار الفلاتر',
        )),

        // زر وضع التحرير
        Obx(() => IconButton(
          icon: Icon(
            _controller.isEditMode ? Icons.edit_off : Icons.edit,
            color: _controller.isEditMode
                ? MondayDashboardConfig.mondayBlue
                : MondayDashboardConfig.textSecondary,
          ),
          onPressed: _controller.toggleEditMode,
          tooltip: _controller.isEditMode ? 'إنهاء التحرير' : 'تحرير التخطيط',
        )),
        
        // زر التحديث
        Obx(() => IconButton(
          icon: _controller.isRefreshing
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      MondayDashboardConfig.mondayBlue,
                    ),
                  ),
                )
              : Icon(
                  Icons.refresh,
                  color: MondayDashboardConfig.textSecondary,
                ),
          onPressed: _controller.isRefreshing ? null : _controller.refreshDashboard,
          tooltip: 'تحديث البيانات',
        )),
        
        // قائمة الخيارات
        PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            color: MondayDashboardConfig.textSecondary,
          ),
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'layouts',
              child: Row(
                children: [
                  Icon(Icons.view_module, size: 16),
                  const SizedBox(width: 8),
                  Text('تخطيطات'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download, size: 16),
                  const SizedBox(width: 8),
                  Text('تصدير'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share, size: 16),
                  const SizedBox(width: 8),
                  Text('مشاركة'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'fullscreen',
              child: Row(
                children: [
                  Icon(Icons.fullscreen, size: 16),
                  const SizedBox(width: 8),
                  Text('ملء الشاشة'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings, size: 16),
                  const SizedBox(width: 8),
                  Text('إعدادات'),
                ],
              ),
            ),
          ],
          onSelected: _handleMenuAction,
        ),
        
        const SizedBox(width: 8),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(1),
        child: Container(
          height: 1,
          color: MondayDashboardConfig.borderColor,
        ),
      ),
    );
  }

  /// بناء شريط التبويبات المحسن
  Widget _buildTabBar() {
    return Container(
      color: MondayDashboardConfig.cardBackground,
      child: TabBar(
        controller: _tabController,
        indicatorColor: MondayDashboardConfig.mondayBlue,
        indicatorWeight: 3,
        labelColor: MondayDashboardConfig.mondayBlue,
        unselectedLabelColor: MondayDashboardConfig.textSecondary,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 14,
        ),
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard_outlined, size: 20),
            text: 'نظرة عامة',
          ),
          Tab(
            icon: Icon(Icons.analytics_outlined, size: 20),
            text: 'التحليلات',
          ),
          Tab(
            icon: Icon(Icons.assessment_outlined, size: 20),
            text: 'التقارير',
          ),
          Tab(
            icon: Icon(Icons.settings_outlined, size: 20),
            text: 'الإعدادات',
          ),
        ],
      ),
    );
  }

  /// بناء قسم البحث
  Widget _buildSearchSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Obx(() => MondaySearchSystem(
        onSearch: _controller.search,
        onAdvancedSearch: _controller.advancedSearch,
        suggestions: _controller.searchSuggestions,
        recentSearches: _controller.recentSearches,
        showAdvancedOptions: true,
        placeholder: 'البحث في لوحة التحكم...',
      )),
    );
  }

  /// بناء قسم الفلاتر
  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Obx(() => MondayFilterSystem(
        availableFilters: [], // سيتم تحديثها من الكونترولر
        activeFilters: _controller.activeFilters,
        onFiltersChanged: (filters) {
          // تطبيق الفلاتر من خلال الكونترولر
        },
        isExpanded: _controller.showFilters,
        onExpandedChanged: (expanded) {
          if (expanded != _controller.showFilters) {
            _controller.toggleFilters();
          }
        },
      )),
    );
  }

  /// بناء تبويب النظرة العامة
  Widget _buildOverviewTab() {
    return Obx(() {
      if (_controller.isLoading) {
        return _buildLoadingState();
      }

      if (_controller.error.isNotEmpty) {
        return _buildErrorState();
      }

      return RefreshIndicator(
        onRefresh: _controller.refreshDashboard,
        color: MondayDashboardConfig.mondayBlue,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              _buildKPISection(),
              _buildChartsSection(),
            ],
          ),
        ),
      );
    });
  }

  /// بناء قسم مؤشرات الأداء الرئيسية
  Widget _buildKPISection() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مؤشرات الأداء الرئيسية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: MondayDashboardConfig.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildKPIGrid(),
        ],
      ),
    );
  }

  /// بناء شبكة مؤشرات الأداء
  Widget _buildKPIGrid() {
    final taskStats = _controller.taskStatistics;
    final userStats = _controller.userStatistics;

    return GridView.count(
      crossAxisCount: _getKPIColumns(),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 2.5,
      children: [
        _buildKPICard(
          'إجمالي المهام',
          taskStats.totalTasks.toString(),
          Icons.assignment,
          MondayDashboardConfig.mondayBlue,
          trend: 12.5,
        ),
        _buildKPICard(
          'المهام المكتملة',
          taskStats.completedTasks.toString(),
          Icons.check_circle,
          MondayDashboardConfig.mondayGreen,
          trend: 8.3,
        ),
        _buildKPICard(
          'قيد التنفيذ',
          taskStats.inProgressTasks.toString(),
          Icons.hourglass_empty,
          MondayDashboardConfig.mondayOrange,
          trend: -2.1,
        ),
        _buildKPICard(
          'المستخدمون النشطون',
          userStats.activeUsers.toString(),
          Icons.people,
          MondayDashboardConfig.mondayPurple,
          trend: 5.7,
        ),
      ],
    );
  }

  /// بناء بطاقة مؤشر أداء
  Widget _buildKPICard(
    String title,
    String value,
    IconData icon,
    Color color, {
    double? trend,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: MondayDashboardConfig.cardBackground,
        borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        border: Border.all(color: MondayDashboardConfig.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: MondayDashboardConfig.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              if (trend != null) ...[
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: trend > 0
                      ? MondayDashboardConfig.mondayGreen.withOpacity(0.1)
                      : MondayDashboardConfig.mondayRed.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        trend > 0 ? Icons.trending_up : Icons.trending_down,
                        size: 14,
                        color: trend > 0
                          ? MondayDashboardConfig.mondayGreen
                          : MondayDashboardConfig.mondayRed,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${trend.abs().toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: trend > 0
                            ? MondayDashboardConfig.mondayGreen
                            : MondayDashboardConfig.mondayRed,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم المخططات
  Widget _buildChartsSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'المخططات والتحليلات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: MondayDashboardConfig.textPrimary,
                ),
              ),
              const Spacer(),
              Obx(() => _controller.isEditMode
                ? ElevatedButton.icon(
                    onPressed: _addNewWidget,
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('إضافة ويدجت'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: MondayDashboardConfig.mondayBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                  )
                : const SizedBox.shrink(),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Obx(() => MondayGridSystem(
            cards: _controller.chartCards,
            onCardsReordered: _controller.reorderChartCards,
            onCardUpdated: _controller.updateCard,
            onRefresh: _controller.refreshDashboard,
            onRefreshSingle: _controller.refreshSingleCard,
            isEditMode: _controller.isEditMode,
            columns: _getGridColumns(),
            spacing: 20.0,
          )),
        ],
      ),
    );
  }

  /// بناء تبويب التحليلات
  Widget _buildAnalyticsTab() {
    return const Center(
      child: Text('تبويب التحليلات - قريباً'),
    );
  }

  /// بناء تبويب التقارير
  Widget _buildReportsTab() {
    return const Center(
      child: Text('تبويب التقارير - قريباً'),
    );
  }

  /// بناء تبويب الإعدادات
  Widget _buildSettingsTab() {
    return const Center(
      child: Text('تبويب الإعدادات - قريباً'),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              MondayDashboardConfig.mondayBlue,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات لوحة التحكم...',
            style: TextStyle(
              color: MondayDashboardConfig.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: MondayDashboardConfig.mondayRed,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: MondayDashboardConfig.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _controller.error,
            style: TextStyle(
              color: MondayDashboardConfig.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _controller.loadDashboardData(forceRefresh: true),
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: MondayDashboardConfig.mondayBlue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر الإجراء العائم
  Widget _buildFloatingActionButton() {
    return Obx(() {
      if (!_controller.isEditMode) return const SizedBox.shrink();

      return FloatingActionButton(
        onPressed: _addNewWidget,
        backgroundColor: MondayDashboardConfig.mondayBlue,
        tooltip: 'إضافة ويدجت جديد',
        child: const Icon(Icons.add, color: Colors.white),
      );
    });
  }

  /// حساب عدد أعمدة مؤشرات الأداء
  int _getKPIColumns() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 768) return 1;
    if (screenWidth < 1024) return 2;
    return 4;
  }

  /// حساب عدد أعمدة الشبكة
  int _getGridColumns() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 768) return 1;
    if (screenWidth < 1024) return 2;
    return 2;
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'layouts':
        _showLayoutsDialog();
        break;
      case 'export':
        _exportDashboard();
        break;
      case 'share':
        _shareDashboard();
        break;
      case 'fullscreen':
        _toggleFullscreen();
        break;
      case 'settings':
        _showSettingsDialog();
        break;
    }
  }

  /// إضافة ويدجت جديد
  void _addNewWidget() {
    // تنفيذ إضافة ويدجت جديد
  }

  /// عرض حوار التخطيطات
  void _showLayoutsDialog() {
    // تنفيذ حوار التخطيطات
  }

  /// تصدير لوحة التحكم
  void _exportDashboard() {
    _controller.exportDashboardData();
  }

  /// مشاركة لوحة التحكم
  void _shareDashboard() {
    // تنفيذ المشاركة
  }

  /// تبديل ملء الشاشة
  void _toggleFullscreen() {
    _controller.toggleFullscreen();
  }

  /// عرض حوار الإعدادات
  void _showSettingsDialog() {
    // تنفيذ حوار الإعدادات
  }
}
