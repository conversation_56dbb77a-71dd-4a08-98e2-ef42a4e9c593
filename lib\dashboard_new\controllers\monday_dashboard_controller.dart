import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dashboard_models.dart';
import '../services/dashboard_data_service.dart';
import '../services/dashboard_preferences_service.dart';
import '../services/monday_sharing_service.dart';
import '../config/monday_dashboard_config.dart';
import '../../controllers/task_controller.dart';
import '../../models/task_models.dart';

/// متحكم لوحة التحكم المحسن بمعايير Monday.com
class MondayDashboardController extends GetxController {
  final DashboardDataService _dataService = DashboardDataService();
  final DashboardPreferencesService _preferencesService = DashboardPreferencesService.instance;

  // الاستفادة من TaskController للبيانات
  TaskController get _taskController => Get.find<TaskController>();

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxBool _isRefreshing = false.obs;
  final RxBool _isExporting = false.obs;

  // بيانات لوحة التحكم
  final Rx<TaskStatistics> _taskStatistics = TaskStatistics.empty().obs;
  final Rx<UserStatistics> _userStatistics = UserStatistics.empty().obs;
  final RxList<ChartCardModel> _chartCards = <ChartCardModel>[].obs;
  final Rx<DashboardSettings> _settings = MondayDashboardConfig.getDefaultSettings().obs;
  final RxList<DashboardLayout> _layouts = <DashboardLayout>[].obs;
  final Rx<DashboardLayout?> _currentLayout = Rx<DashboardLayout?>(null);

  // البحث والفلترة
  final RxString _searchQuery = ''.obs;
  final RxMap<String, DataFilter> _activeFilters = <String, DataFilter>{}.obs;
  final RxList<String> _searchSuggestions = <String>[].obs;
  final RxList<String> _recentSearches = <String>[].obs;

  // إعدادات العرض
  final RxBool _isEditMode = false.obs;
  final RxBool _showFilters = false.obs;
  final RxBool _isFullscreen = false.obs;
  final RxInt _selectedTabIndex = 0.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  bool get isRefreshing => _isRefreshing.value;
  bool get isExporting => _isExporting.value;
  TaskStatistics get taskStatistics => _taskStatistics.value;
  UserStatistics get userStatistics => _userStatistics.value;
  List<ChartCardModel> get chartCards => _chartCards;
  DashboardSettings get settings => _settings.value;
  List<DashboardLayout> get layouts => _layouts;
  DashboardLayout? get currentLayout => _currentLayout.value;
  String get searchQuery => _searchQuery.value;
  Map<String, DataFilter> get activeFilters => _activeFilters;
  List<String> get searchSuggestions => _searchSuggestions;
  List<String> get recentSearches => _recentSearches;
  bool get isEditMode => _isEditMode.value;
  bool get showFilters => _showFilters.value;
  bool get isFullscreen => _isFullscreen.value;
  int get selectedTabIndex => _selectedTabIndex.value;

  @override
  void onInit() {
    super.onInit();
    _initializeDashboard();
  }

  /// تهيئة لوحة التحكم
  Future<void> _initializeDashboard() async {
    try {
      _isLoading.value = true;
      _error.value = '';

      // تحميل الإعدادات المحفوظة
      await _loadSavedSettings();
      
      // تحميل التخطيطات الافتراضية
      _loadDefaultLayouts();
      
      // تحميل البيانات
      await loadDashboardData();
      
      // تحميل عمليات البحث الأخيرة
      await _loadRecentSearches();
      
    } catch (e) {
      _error.value = 'خطأ في تهيئة لوحة التحكم: $e';
      debugPrint('خطأ في تهيئة لوحة التحكم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل بيانات لوحة التحكم
  Future<void> loadDashboardData({bool forceRefresh = false}) async {
    try {
      if (forceRefresh) {
        _isRefreshing.value = true;
      } else {
        _isLoading.value = true;
      }
      _error.value = '';

      // تحميل إحصائيات المهام
      await _loadTaskStatistics();
      
      // تحميل إحصائيات المستخدمين
      await _loadUserStatistics();
      
      // تحميل بطاقات المخططات
      await _loadChartCards();
      
      // تحديث اقتراحات البحث
      await _updateSearchSuggestions();

    } catch (e) {
      _error.value = 'خطأ في تحميل البيانات: $e';
      debugPrint('خطأ في تحميل بيانات لوحة التحكم: $e');
    } finally {
      _isLoading.value = false;
      _isRefreshing.value = false;
    }
  }

  /// تحميل إحصائيات المهام
  Future<void> _loadTaskStatistics() async {
    try {
      final tasks = _taskController.allTasks;
      
      final totalTasks = tasks.length;
      final completedTasks = tasks.where((t) => t.status == 'completed').length;
      final inProgressTasks = tasks.where((t) => t.status == 'in_progress').length;
      final pendingTasks = tasks.where((t) => t.status == 'pending').length;
      final overdueTasks = tasks.where((t) => 
        t.dueDate != null && 
        t.dueDate!.isBefore(DateTime.now()) && 
        t.status != 'completed'
      ).length;

      _taskStatistics.value = TaskStatistics(
        totalTasks: totalTasks,
        completedTasks: completedTasks,
        inProgressTasks: inProgressTasks,
        pendingTasks: pendingTasks,
        overdueTasks: overdueTasks,
        completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
      );
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات المهام: $e');
    }
  }

  /// تحميل إحصائيات المستخدمين
  Future<void> _loadUserStatistics() async {
    try {
      // يمكن الحصول على هذه البيانات من UserController أو API
      _userStatistics.value = const UserStatistics(
        totalUsers: 25,
        activeUsers: 18,
        onlineUsers: 12,
        newUsersThisMonth: 3,
      );
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات المستخدمين: $e');
    }
  }

  /// تحميل بطاقات المخططات
  Future<void> _loadChartCards() async {
    try {
      final defaultCards = MondayDashboardConfig.getDefaultChartCards();
      
      // تحديث البيانات لكل بطاقة
      for (var card in defaultCards) {
        await _updateCardData(card);
      }
      
      _chartCards.value = defaultCards;
    } catch (e) {
      debugPrint('خطأ في تحميل بطاقات المخططات: $e');
    }
  }

  /// تحديث بيانات البطاقة
  Future<void> _updateCardData(ChartCardModel card) async {
    try {
      List<ChartDataPoint> data = [];
      
      switch (card.dataSource) {
        case 'tasks_status':
          data = await _getTaskStatusData();
          break;
        case 'tasks_priority':
          data = await _getTaskPriorityData();
          break;
        case 'tasks_department':
          data = await _getTaskDepartmentData();
          break;
        case 'tasks_assignee':
          data = await _getTaskAssigneeData();
          break;
        case 'user_activity':
          data = await _getUserActivityData();
          break;
        case 'task_trends':
          data = await _getTaskTrendsData();
          break;
        default:
          data = _generateSampleData();
      }
      
      card.data = data;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات البطاقة ${card.id}: $e');
    }
  }

  /// الحصول على بيانات حالة المهام
  Future<List<ChartDataPoint>> _getTaskStatusData() async {
    final tasks = _taskController.allTasks;
    final statusCounts = <String, int>{};
    
    for (var task in tasks) {
      statusCounts[task.status] = (statusCounts[task.status] ?? 0) + 1;
    }
    
    return statusCounts.entries.map((entry) => ChartDataPoint(
      label: _getStatusLabel(entry.key),
      value: entry.value.toDouble(),
      color: MondayDashboardConfig.statusColors[entry.key],
    )).toList();
  }

  /// الحصول على بيانات أولوية المهام
  Future<List<ChartDataPoint>> _getTaskPriorityData() async {
    final tasks = _taskController.allTasks;
    final priorityCounts = <String, int>{};
    
    for (var task in tasks) {
      final priority = task.priority ?? 'منخفضة';
      priorityCounts[priority] = (priorityCounts[priority] ?? 0) + 1;
    }
    
    return priorityCounts.entries.map((entry) => ChartDataPoint(
      label: entry.key,
      value: entry.value.toDouble(),
      color: MondayDashboardConfig.priorityColors[entry.key],
    )).toList();
  }

  /// الحصول على بيانات أقسام المهام
  Future<List<ChartDataPoint>> _getTaskDepartmentData() async {
    final tasks = _taskController.allTasks;
    final departmentCounts = <String, int>{};
    
    for (var task in tasks) {
      final department = task.departmentName ?? 'غير محدد';
      departmentCounts[department] = (departmentCounts[department] ?? 0) + 1;
    }
    
    return departmentCounts.entries.map((entry) => ChartDataPoint(
      label: entry.key,
      value: entry.value.toDouble(),
      color: MondayDashboardConfig.mondayColorPalette[
        entry.key.hashCode % MondayDashboardConfig.mondayColorPalette.length
      ],
    )).toList();
  }

  /// الحصول على بيانات المكلفين بالمهام
  Future<List<ChartDataPoint>> _getTaskAssigneeData() async {
    final tasks = _taskController.allTasks;
    final assigneeCounts = <String, int>{};
    
    for (var task in tasks) {
      final assignee = task.assigneeName ?? 'غير مكلف';
      assigneeCounts[assignee] = (assigneeCounts[assignee] ?? 0) + 1;
    }
    
    return assigneeCounts.entries.take(10).map((entry) => ChartDataPoint(
      label: entry.key,
      value: entry.value.toDouble(),
      color: MondayDashboardConfig.mondayColorPalette[
        entry.key.hashCode % MondayDashboardConfig.mondayColorPalette.length
      ],
    )).toList();
  }

  /// الحصول على بيانات نشاط المستخدمين
  Future<List<ChartDataPoint>> _getUserActivityData() async {
    // بيانات تجريبية - يمكن استبدالها ببيانات حقيقية
    final now = DateTime.now();
    final data = <ChartDataPoint>[];
    
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final activity = 10 + (i * 5) + (date.weekday % 3) * 8;
      
      data.add(ChartDataPoint(
        label: '${date.day}/${date.month}',
        value: activity.toDouble(),
        color: MondayDashboardConfig.mondayBlue,
      ));
    }
    
    return data;
  }

  /// الحصول على بيانات اتجاهات المهام
  Future<List<ChartDataPoint>> _getTaskTrendsData() async {
    // بيانات تجريبية - يمكن استبدالها ببيانات حقيقية
    final now = DateTime.now();
    final data = <ChartDataPoint>[];
    
    for (int i = 11; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final completed = 15 + (i % 4) * 8;
      
      data.add(ChartDataPoint(
        label: '${month.month}/${month.year}',
        value: completed.toDouble(),
        color: MondayDashboardConfig.mondayGreen,
      ));
    }
    
    return data;
  }

  /// توليد بيانات تجريبية
  List<ChartDataPoint> _generateSampleData() {
    return [
      ChartDataPoint(
        label: 'عينة 1',
        value: 25.0,
        color: MondayDashboardConfig.mondayBlue,
      ),
      ChartDataPoint(
        label: 'عينة 2',
        value: 35.0,
        color: MondayDashboardConfig.mondayGreen,
      ),
      ChartDataPoint(
        label: 'عينة 3',
        value: 20.0,
        color: MondayDashboardConfig.mondayOrange,
      ),
    ];
  }

  /// تحديث اقتراحات البحث
  Future<void> _updateSearchSuggestions() async {
    try {
      final suggestions = <String>[];
      
      // إضافة أسماء المهام
      final tasks = _taskController.allTasks;
      suggestions.addAll(tasks.map((t) => t.title).take(10));
      
      // إضافة أسماء الأقسام
      final departments = tasks.map((t) => t.departmentName).where((d) => d != null).toSet();
      suggestions.addAll(departments.cast<String>());
      
      // إضافة أسماء المكلفين
      final assignees = tasks.map((t) => t.assigneeName).where((a) => a != null).toSet();
      suggestions.addAll(assignees.cast<String>());
      
      _searchSuggestions.value = suggestions.toSet().toList();
    } catch (e) {
      debugPrint('خطأ في تحديث اقتراحات البحث: $e');
    }
  }

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSavedSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل إعدادات العرض
      _isEditMode.value = prefs.getBool('dashboard_edit_mode') ?? false;
      _showFilters.value = prefs.getBool('dashboard_show_filters') ?? false;
      _selectedTabIndex.value = prefs.getInt('dashboard_selected_tab') ?? 0;
      
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
  }

  /// تحميل التخطيطات الافتراضية
  void _loadDefaultLayouts() {
    _layouts.value = MondayDashboardConfig.getDefaultLayouts();
    _currentLayout.value = _layouts.first;
  }

  /// تحميل عمليات البحث الأخيرة
  Future<void> _loadRecentSearches() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final searches = prefs.getStringList('recent_searches') ?? [];
      _recentSearches.value = searches;
    } catch (e) {
      debugPrint('خطأ في تحميل عمليات البحث الأخيرة: $e');
    }
  }

  /// تحديث لوحة التحكم
  Future<void> refreshDashboard() async {
    await loadDashboardData(forceRefresh: true);
  }

  /// تحديث بطاقة واحدة
  Future<void> refreshSingleCard(String cardId) async {
    try {
      final cardIndex = _chartCards.indexWhere((card) => card.id == cardId);
      if (cardIndex != -1) {
        await _updateCardData(_chartCards[cardIndex]);
        _chartCards.refresh();
      }
    } catch (e) {
      debugPrint('خطأ في تحديث البطاقة $cardId: $e');
    }
  }

  /// إعادة ترتيب البطاقات
  void reorderChartCards(List<ChartCardModel> newOrder) {
    _chartCards.value = newOrder;
    _saveLayoutChanges();
  }

  /// تحديث بطاقة
  void updateCard(ChartCardModel updatedCard) {
    final index = _chartCards.indexWhere((card) => card.id == updatedCard.id);
    if (index != -1) {
      _chartCards[index] = updatedCard;
      _chartCards.refresh();
      _saveLayoutChanges();
    }
  }

  /// البحث
  void search(String query) {
    _searchQuery.value = query;
    _addToRecentSearches(query);
    // تطبيق البحث على البيانات
    _applySearchAndFilters();
  }

  /// البحث المتقدم
  void advancedSearch(Map<String, dynamic> filters) {
    // تحويل الفلاتر إلى DataFilter objects
    final dataFilters = <String, DataFilter>{};
    
    filters.forEach((key, value) {
      if (value != null) {
        dataFilters[key] = DataFilter(
          id: key,
          name: key,
          type: FilterType.search,
          value: value,
          isActive: true,
        );
      }
    });
    
    _activeFilters.value = dataFilters;
    _applySearchAndFilters();
  }

  /// تطبيق البحث والفلاتر
  void _applySearchAndFilters() {
    // تطبيق البحث والفلاتر على البيانات
    // يمكن تحديث البطاقات بناءً على النتائج
  }

  /// إضافة إلى عمليات البحث الأخيرة
  Future<void> _addToRecentSearches(String query) async {
    if (query.isEmpty) return;
    
    try {
      final searches = List<String>.from(_recentSearches);
      searches.remove(query); // إزالة إذا كان موجود
      searches.insert(0, query); // إضافة في المقدمة
      
      if (searches.length > 10) {
        searches.removeRange(10, searches.length);
      }
      
      _recentSearches.value = searches;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('recent_searches', searches);
    } catch (e) {
      debugPrint('خطأ في حفظ البحث الأخير: $e');
    }
  }

  /// تبديل وضع التحرير
  void toggleEditMode() {
    _isEditMode.value = !_isEditMode.value;
    _saveViewSettings();
  }

  /// تبديل عرض الفلاتر
  void toggleFilters() {
    _showFilters.value = !_showFilters.value;
    _saveViewSettings();
  }

  /// تبديل ملء الشاشة
  void toggleFullscreen() {
    _isFullscreen.value = !_isFullscreen.value;
  }

  /// تغيير التبويب
  void changeTab(int index) {
    _selectedTabIndex.value = index;
    _saveViewSettings();
  }

  /// مشاركة لوحة التحكم
  Future<void> shareDashboard() async {
    try {
      await MondaySharingService.showShareDialog(
        context: Get.context!,
        dashboardId: 'main_dashboard',
        dashboardTitle: 'لوحة التحكم الرئيسية',
      );
    } catch (e) {
      debugPrint('خطأ في مشاركة لوحة التحكم: $e');
    }
  }

  /// تصدير بيانات لوحة التحكم
  Future<void> exportDashboardData() async {
    try {
      _isExporting.value = true;
      
      // تنفيذ التصدير
      await Future.delayed(const Duration(seconds: 2)); // محاكاة التصدير
      
      Get.snackbar(
        'تم التصدير',
        'تم تصدير بيانات لوحة التحكم بنجاح',
        backgroundColor: MondayDashboardConfig.mondayGreen,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير البيانات',
        backgroundColor: MondayDashboardConfig.mondayRed,
        colorText: Colors.white,
      );
    } finally {
      _isExporting.value = false;
    }
  }

  /// حفظ تغييرات التخطيط
  Future<void> _saveLayoutChanges() async {
    try {
      // حفظ التخطيط الحالي
      final prefs = await SharedPreferences.getInstance();
      // يمكن حفظ ترتيب البطاقات هنا
    } catch (e) {
      debugPrint('خطأ في حفظ تغييرات التخطيط: $e');
    }
  }

  /// حفظ إعدادات العرض
  Future<void> _saveViewSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('dashboard_edit_mode', _isEditMode.value);
      await prefs.setBool('dashboard_show_filters', _showFilters.value);
      await prefs.setInt('dashboard_selected_tab', _selectedTabIndex.value);
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات العرض: $e');
    }
  }

  /// الحصول على تسمية الحالة
  String _getStatusLabel(String status) {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'pending':
        return 'معلقة';
      case 'overdue':
        return 'متأخرة';
      default:
        return status;
    }
  }
}
