import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'screens/monday_dashboard_screen.dart';
import 'controllers/monday_dashboard_controller.dart';
import 'config/monday_dashboard_config.dart';

/// تطبيق لوحة التحكم المحسن بمعايير Monday.com
class MondayDashboardApp extends StatelessWidget {
  const MondayDashboardApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'لوحة التحكم - Monday Style',
      debugShowCheckedModeBanner: false,
      theme: _buildTheme(),
      home: const MondayDashboardScreen(),
      initialBinding: DashboardBinding(),
      locale: const Locale('ar', 'SA'),
      fallbackLocale: const Locale('en', 'US'),
      textDirection: TextDirection.rtl,
    );
  }

  /// بناء ثيم التطبيق بمعايير Monday.com
  ThemeData _buildTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: MondayDashboardConfig.mondayBlue,
        brightness: Brightness.light,
      ),
      
      // ألوان أساسية
      primaryColor: MondayDashboardConfig.mondayBlue,
      scaffoldBackgroundColor: MondayDashboardConfig.backgroundColor,
      cardColor: MondayDashboardConfig.cardBackground,
      
      // خطوط
      fontFamily: 'Cairo',
      textTheme: TextTheme(
        displayLarge: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        displaySmall: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontWeight: FontWeight.w500,
        ),
        headlineLarge: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        headlineSmall: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontWeight: FontWeight.w500,
        ),
        titleLarge: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        titleMedium: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontWeight: FontWeight.w500,
        ),
        titleSmall: TextStyle(
          color: MondayDashboardConfig.textSecondary,
          fontWeight: FontWeight.w500,
        ),
        bodyLarge: TextStyle(
          color: MondayDashboardConfig.textPrimary,
        ),
        bodyMedium: TextStyle(
          color: MondayDashboardConfig.textSecondary,
        ),
        bodySmall: TextStyle(
          color: MondayDashboardConfig.textMuted,
        ),
        labelLarge: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontWeight: FontWeight.w500,
        ),
        labelMedium: TextStyle(
          color: MondayDashboardConfig.textSecondary,
        ),
        labelSmall: TextStyle(
          color: MondayDashboardConfig.textMuted,
        ),
      ),
      
      // شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: MondayDashboardConfig.cardBackground,
        foregroundColor: MondayDashboardConfig.textPrimary,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: 'Cairo',
        ),
        iconTheme: IconThemeData(
          color: MondayDashboardConfig.textSecondary,
        ),
      ),
      
      // البطاقات
      cardTheme: CardTheme(
        color: MondayDashboardConfig.cardBackground,
        elevation: MondayDashboardConfig.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
          side: BorderSide(
            color: MondayDashboardConfig.borderColor,
            width: 1,
          ),
        ),
        margin: const EdgeInsets.all(8),
      ),
      
      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: MondayDashboardConfig.mondayBlue,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          textStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: MondayDashboardConfig.mondayBlue,
          side: BorderSide(color: MondayDashboardConfig.mondayBlue),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          textStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: MondayDashboardConfig.mondayBlue,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          textStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ),
      
      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: MondayDashboardConfig.cardBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.mondayBlue, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: MondayDashboardConfig.mondayRed),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        hintStyle: TextStyle(
          color: MondayDashboardConfig.textMuted,
        ),
        labelStyle: TextStyle(
          color: MondayDashboardConfig.textSecondary,
        ),
      ),
      
      // التبويبات
      tabBarTheme: TabBarTheme(
        labelColor: MondayDashboardConfig.mondayBlue,
        unselectedLabelColor: MondayDashboardConfig.textSecondary,
        indicatorColor: MondayDashboardConfig.mondayBlue,
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 14,
        ),
      ),
      
      // الحوارات
      dialogTheme: DialogTheme(
        backgroundColor: MondayDashboardConfig.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(MondayDashboardConfig.cardBorderRadius),
        ),
        elevation: 8,
        titleTextStyle: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        contentTextStyle: TextStyle(
          color: MondayDashboardConfig.textSecondary,
          fontSize: 14,
        ),
      ),
      
      // شريط التمرير
      scrollbarTheme: ScrollbarThemeData(
        thumbColor: MaterialStateProperty.all(
          MondayDashboardConfig.textMuted.withOpacity(0.5),
        ),
        trackColor: MaterialStateProperty.all(
          MondayDashboardConfig.borderColor.withOpacity(0.3),
        ),
        radius: const Radius.circular(4),
        thickness: MaterialStateProperty.all(6),
      ),
      
      // الفواصل
      dividerTheme: DividerThemeData(
        color: MondayDashboardConfig.borderColor,
        thickness: 1,
        space: 1,
      ),
      
      // أشرطة التقدم
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: MondayDashboardConfig.mondayBlue,
        linearTrackColor: MondayDashboardConfig.borderColor,
        circularTrackColor: MondayDashboardConfig.borderColor,
      ),
      
      // الرقائق
      chipTheme: ChipThemeData(
        backgroundColor: MondayDashboardConfig.hoverColor,
        selectedColor: MondayDashboardConfig.mondayBlue.withOpacity(0.1),
        disabledColor: MondayDashboardConfig.borderColor,
        labelStyle: TextStyle(
          color: MondayDashboardConfig.textPrimary,
          fontSize: 12,
        ),
        secondaryLabelStyle: TextStyle(
          color: MondayDashboardConfig.mondayBlue,
          fontSize: 12,
        ),
        brightness: Brightness.light,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: MondayDashboardConfig.borderColor),
        ),
      ),
      
      // الأيقونات
      iconTheme: IconThemeData(
        color: MondayDashboardConfig.textSecondary,
        size: 24,
      ),
      
      // الزر العائم
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: MondayDashboardConfig.mondayBlue,
        foregroundColor: Colors.white,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}

/// ربط التبعيات
class DashboardBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<MondayDashboardController>(() => MondayDashboardController());
  }
}
