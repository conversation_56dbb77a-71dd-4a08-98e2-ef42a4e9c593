import 'package:get/get.dart';
import '../controllers/monday_dashboard_controller.dart';
import '../services/dashboard_data_service.dart';
import '../services/dashboard_preferences_service.dart';
import '../services/monday_sharing_service.dart';

/// Binding لنظام Monday Dashboard
class MondayDashboardBinding extends Bindings {
  @override
  void dependencies() {
    // تسجيل الخدمات
    Get.lazyPut<DashboardDataService>(() => DashboardDataService());
    Get.lazyPut<DashboardPreferencesService>(() => DashboardPreferencesService.instance);
    Get.lazyPut<MondaySharingService>(() => MondaySharingService());

    // تسجيل الكونترولر
    Get.lazyPut<MondayDashboardController>(() => MondayDashboardController());
  }
}
